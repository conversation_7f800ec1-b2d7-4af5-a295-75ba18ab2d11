'use client'

import { useState, useEffect } from 'react'
import { Star, Download, Search, Filter } from 'lucide-react'
import { createSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuth'

interface Work {
  id: string
  title: string
  description: string | null
  subject: string
  course: number
  work_type: 'lab' | 'practical' | 'coursework' | 'other'
  file_url: string
  file_name: string
  file_size: number
  author_id: string
  rating: number
  rating_count: number
  created_at: string
  profiles?: {
    full_name: string | null
  }
}

export default function WorksList() {
  const [works, setWorks] = useState<Work[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCourse, setSelectedCourse] = useState<number | ''>('')
  const [selectedType, setSelectedType] = useState<string>('')
  const { user } = useAuth()
  const supabase = createSupabaseClient()

  useEffect(() => {
    fetchWorks()
  }, [])

  const fetchWorks = async () => {
    try {
      if (!supabase) {
        // Demo mode - load from localStorage and add some sample data
        const savedWorks = JSON.parse(localStorage.getItem('demo-works') || '[]')
        const sampleWorks = [
          {
            id: 'sample-1',
            title: 'Лабораторна робота №1 з програмування',
            description: 'Основи роботи з масивами та циклами',
            subject: 'Програмування',
            course: 1,
            work_type: 'lab',
            file_url: '#demo-file',
            file_name: 'lab1_programming.pdf',
            file_size: 1024000,
            author_id: 'sample-user',
            rating: 4.5,
            rating_count: 12,
            created_at: '2024-01-15T10:00:00Z',
            profiles: { full_name: 'Іван Петренко' }
          },
          {
            id: 'sample-2',
            title: 'Практична робота з математики',
            description: 'Розв\'язання диференціальних рівнянь',
            subject: 'Математика',
            course: 2,
            work_type: 'practical',
            file_url: '#demo-file',
            file_name: 'math_practical.docx',
            file_size: 512000,
            author_id: 'sample-user-2',
            rating: 4.2,
            rating_count: 8,
            created_at: '2024-01-10T14:30:00Z',
            profiles: { full_name: 'Марія Коваленко' }
          },
          {
            id: 'sample-3',
            title: 'Курсова робота з фізики',
            description: 'Дослідження електромагнітних хвиль',
            subject: 'Фізика',
            course: 3,
            work_type: 'coursework',
            file_url: '#demo-file',
            file_name: 'physics_coursework.pdf',
            file_size: 2048000,
            author_id: 'sample-user-3',
            rating: 4.8,
            rating_count: 15,
            created_at: '2024-01-05T09:15:00Z',
            profiles: { full_name: 'Олександр Сидоренко' }
          }
        ]

        const allWorks = [...sampleWorks, ...savedWorks]
        setWorks(allWorks)
        setLoading(false)
        return
      }

      const { data, error } = await supabase
        .from('works')
        .select(`
          *,
          profiles (
            full_name
          )
        `)
        .order('created_at', { ascending: false })

      if (error) throw error
      setWorks(data || [])
    } catch (error) {
      console.error('Error fetching works:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRating = async (workId: string, rating: number) => {
    if (!user) {
      alert('Ви повинні увійти в систему для оцінювання')
      return
    }

    try {
      if (!supabase) {
        // Demo mode - update localStorage
        const allWorks = [...works]
        const workIndex = allWorks.findIndex(w => w.id === workId)
        if (workIndex !== -1) {
          const work = allWorks[workIndex]
          const newRatingCount = work.rating_count + 1
          const newRating = ((work.rating * work.rating_count) + rating) / newRatingCount

          allWorks[workIndex] = {
            ...work,
            rating: newRating,
            rating_count: newRatingCount
          }

          setWorks(allWorks)

          // Update localStorage if it's a user-created work
          const savedWorks = JSON.parse(localStorage.getItem('demo-works') || '[]')
          const savedWorkIndex = savedWorks.findIndex((w: any) => w.id === workId)
          if (savedWorkIndex !== -1) {
            savedWorks[savedWorkIndex] = allWorks[workIndex]
            localStorage.setItem('demo-works', JSON.stringify(savedWorks))
          }
        }
        return
      }

      // Check if user already rated this work
      const { data: existingRating } = await supabase
        .from('ratings')
        .select('*')
        .eq('work_id', workId)
        .eq('user_id', user.id)
        .single()

      if (existingRating) {
        // Update existing rating
        await supabase
          .from('ratings')
          .update({ rating })
          .eq('id', existingRating.id)
      } else {
        // Create new rating
        await supabase
          .from('ratings')
          .insert({
            work_id: workId,
            user_id: user.id,
            rating,
          })
      }

      // Recalculate work rating
      const { data: ratings } = await supabase
        .from('ratings')
        .select('rating')
        .eq('work_id', workId)

      if (ratings) {
        const avgRating = ratings.reduce((sum, r) => sum + r.rating, 0) / ratings.length
        const ratingCount = ratings.length

        await supabase
          .from('works')
          .update({
            rating: avgRating,
            rating_count: ratingCount,
          })
          .eq('id', workId)

        // Refresh works list
        fetchWorks()
      }
    } catch (error) {
      console.error('Error rating work:', error)
      alert('Помилка при оцінюванні роботи')
    }
  }

  const filteredWorks = works.filter(work => {
    const matchesSearch = work.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         work.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         work.description?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesCourse = selectedCourse === '' || work.course === selectedCourse
    const matchesType = selectedType === '' || work.work_type === selectedType

    return matchesSearch && matchesCourse && matchesType
  })

  const getWorkTypeLabel = (type: string) => {
    const types = {
      lab: 'Лабораторна',
      practical: 'Практична',
      coursework: 'Курсова',
      other: 'Інше',
    }
    return types[type as keyof typeof types] || type
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('uk-UA')
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Search and Filters */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Пошук робіт..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div className="flex gap-4">
            <select
              value={selectedCourse}
              onChange={(e) => setSelectedCourse(e.target.value === '' ? '' : Number(e.target.value))}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Всі курси</option>
              {[1, 2, 3, 4, 5, 6].map(course => (
                <option key={course} value={course}>{course} курс</option>
              ))}
            </select>

            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Всі типи</option>
              <option value="lab">Лабораторні</option>
              <option value="practical">Практичні</option>
              <option value="coursework">Курсові</option>
              <option value="other">Інше</option>
            </select>
          </div>
        </div>
      </div>

      {/* Works Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredWorks.map((work) => (
          <div key={work.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <div className="flex justify-between items-start mb-3">
              <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                {getWorkTypeLabel(work.work_type)}
              </span>
              <span className="text-sm text-gray-500">{work.course} курс</span>
            </div>

            <h3 className="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">
              {work.title}
            </h3>

            <p className="text-sm text-gray-600 mb-2">
              <strong>Предмет:</strong> {work.subject}
            </p>

            {work.description && (
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                {work.description}
              </p>
            )}

            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    onClick={() => handleRating(work.id, star)}
                    className="focus:outline-none"
                  >
                    <Star
                      className={`h-4 w-4 ${
                        star <= work.rating
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      } hover:text-yellow-400 transition-colors`}
                    />
                  </button>
                ))}
                <span className="text-sm text-gray-500 ml-2">
                  ({work.rating_count})
                </span>
              </div>
            </div>

            <div className="text-xs text-gray-500 mb-3">
              <p>Автор: {work.profiles?.full_name || 'Анонім'}</p>
              <p>Розмір: {formatFileSize(work.file_size)}</p>
              <p>Дата: {formatDate(work.created_at)}</p>
            </div>

            <a
              href={work.file_url}
              download={work.file_name}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
            >
              <Download className="h-4 w-4" />
              <span>Завантажити</span>
            </a>
          </div>
        ))}
      </div>

      {filteredWorks.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">Роботи не знайдено</p>
          <p className="text-gray-400 text-sm mt-2">
            Спробуйте змінити параметри пошуку або завантажте першу роботу!
          </p>
        </div>
      )}
    </div>
  )
}
