# Supabase Configuration
# Розкоментуйте та заповніть ці поля після створення проекту Supabase:

# NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
# SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Інструкції:
# 1. Створіть проект на https://supabase.com
# 2. Перейдіть в Settings → API
# 3. Скопіюйте Project URL в NEXT_PUBLIC_SUPABASE_URL
# 4. Скопіюйте anon public key в NEXT_PUBLIC_SUPABASE_ANON_KEY
# 5. Скопіюйте service_role key в SUPABASE_SERVICE_ROLE_KEY
# 6. Виконайте SQL скрипт з файлу supabase-setup.sql в SQL Editor
# 7. Перезапустіть сервер: npm run dev

# Поки ці поля не заповнені, додаток працює в демо-режимі
# Демо логін: username=demo, password=demo123
