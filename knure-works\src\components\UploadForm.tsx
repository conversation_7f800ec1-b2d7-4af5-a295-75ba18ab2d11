'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Upload, FileText } from 'lucide-react'
import { createSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuth'

const uploadSchema = z.object({
  title: z.string().min(3, 'Назва повинна містити мінімум 3 символи'),
  description: z.string().optional(),
  subject: z.string().min(2, 'Предмет повинен містити мінімум 2 символи'),
  course: z.number().min(1).max(6),
  workType: z.enum(['lab', 'practical', 'coursework', 'other']),
})

type UploadFormData = z.infer<typeof uploadSchema>

interface UploadFormProps {
  onSuccess?: () => void
}

export default function UploadForm({ onSuccess }: UploadFormProps) {
  const [file, setFile] = useState<File | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { user } = useAuth()
  const supabase = createSupabaseClient()

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<UploadFormData>({
    resolver: zodResolver(uploadSchema),
  })

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      // Check file size (max 10MB)
      if (selectedFile.size > 10 * 1024 * 1024) {
        setError('Файл занадто великий. Максимальний розмір: 10MB')
        return
      }
      setFile(selectedFile)
      setError(null)
    }
  }

  const onSubmit = async (data: UploadFormData) => {
    if (!file) {
      setError('Будь ласка, оберіть файл')
      return
    }

    if (!user) {
      setError('Ви повинні увійти в систему')
      return
    }

    setLoading(true)
    setError(null)

    try {
      if (!supabase) {
        // Demo mode - simulate upload
        await new Promise(resolve => setTimeout(resolve, 2000))

        // Save to localStorage for demo
        const works = JSON.parse(localStorage.getItem('demo-works') || '[]')
        const newWork = {
          id: Date.now().toString(),
          title: data.title,
          description: data.description || null,
          subject: data.subject,
          course: data.course,
          work_type: data.workType,
          file_url: '#demo-file',
          file_name: file.name,
          file_size: file.size,
          author_id: user.id,
          rating: 0,
          rating_count: 0,
          created_at: new Date().toISOString(),
          profiles: {
            full_name: user.user_metadata?.full_name || 'Демо Користувач'
          }
        }
        works.push(newWork)
        localStorage.setItem('demo-works', JSON.stringify(works))

        reset()
        setFile(null)
        onSuccess?.()
        alert('Роботу успішно завантажено! (Демо режим)')
        return
      }

      // Upload file to Supabase Storage
      const fileExt = file.name.split('.').pop()
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
      const filePath = `works/${fileName}`

      const { error: uploadError } = await supabase.storage
        .from('files')
        .upload(filePath, file)

      if (uploadError) {
        throw uploadError
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('files')
        .getPublicUrl(filePath)

      // Save work info to database
      const { error: dbError } = await supabase
        .from('works')
        .insert({
          title: data.title,
          description: data.description || null,
          subject: data.subject,
          course: data.course,
          work_type: data.workType,
          file_url: publicUrl,
          file_name: file.name,
          file_size: file.size,
          author_id: user.id,
        })

      if (dbError) {
        throw dbError
      }

      reset()
      setFile(null)
      onSuccess?.()
      alert('Роботу успішно завантажено!')
    } catch (err: any) {
      setError(err.message || 'Сталася помилка при завантаженні')
    } finally {
      setLoading(false)
    }
  }

  const workTypeOptions = [
    { value: 'lab', label: 'Лабораторна робота' },
    { value: 'practical', label: 'Практична робота' },
    { value: 'coursework', label: 'Курсова робота' },
    { value: 'other', label: 'Інше' },
  ]

  return (
    <div className="max-w-2xl mx-auto bg-white p-8 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold text-center mb-6 text-gray-800">
        Завантажити роботу
      </h2>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
            Назва роботи *
          </label>
          <input
            {...register('title')}
            type="text"
            id="title"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Наприклад: Лабораторна робота №1 з програмування"
          />
          {errors.title && (
            <p className="text-red-500 text-sm mt-1">{errors.title.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            Опис роботи
          </label>
          <textarea
            {...register('description')}
            id="description"
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Короткий опис роботи, що вона включає..."
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
              Предмет *
            </label>
            <input
              {...register('subject')}
              type="text"
              id="subject"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Наприклад: Програмування"
            />
            {errors.subject && (
              <p className="text-red-500 text-sm mt-1">{errors.subject.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="course" className="block text-sm font-medium text-gray-700 mb-1">
              Курс *
            </label>
            <select
              {...register('course', { valueAsNumber: true })}
              id="course"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Оберіть курс</option>
              {[1, 2, 3, 4, 5, 6].map(course => (
                <option key={course} value={course}>{course} курс</option>
              ))}
            </select>
            {errors.course && (
              <p className="text-red-500 text-sm mt-1">{errors.course.message}</p>
            )}
          </div>
        </div>

        <div>
          <label htmlFor="workType" className="block text-sm font-medium text-gray-700 mb-1">
            Тип роботи *
          </label>
          <select
            {...register('workType')}
            id="workType"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Оберіть тип роботи</option>
            {workTypeOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {errors.workType && (
            <p className="text-red-500 text-sm mt-1">{errors.workType.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="file" className="block text-sm font-medium text-gray-700 mb-1">
            Файл роботи *
          </label>
          <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
            <div className="space-y-1 text-center">
              {file ? (
                <div className="flex items-center justify-center space-x-2">
                  <FileText className="h-8 w-8 text-blue-500" />
                  <span className="text-sm text-gray-600">{file.name}</span>
                </div>
              ) : (
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
              )}
              <div className="flex text-sm text-gray-600">
                <label
                  htmlFor="file"
                  className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                >
                  <span>{file ? 'Змінити файл' : 'Завантажити файл'}</span>
                  <input
                    id="file"
                    name="file"
                    type="file"
                    className="sr-only"
                    onChange={handleFileChange}
                    accept=".pdf,.doc,.docx,.txt,.zip,.rar"
                  />
                </label>
              </div>
              <p className="text-xs text-gray-500">
                PDF, DOC, DOCX, TXT, ZIP, RAR до 10MB
              </p>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        <button
          type="submit"
          disabled={loading || !file}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
        >
          {loading ? (
            <span>Завантаження...</span>
          ) : (
            <>
              <Upload className="h-5 w-5" />
              <span>Завантажити роботу</span>
            </>
          )}
        </button>
      </form>
    </div>
  )
}
