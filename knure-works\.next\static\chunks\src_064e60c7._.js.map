{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D0%A0%D0%BE%D0%B1%D0%BE%D1%87%D0%B8%D0%B9%20%D1%81%D1%82%D1%96%D0%BB/ai/knure-works/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n// Check if we have real Supabase credentials\nconst isDemo = !supabaseUrl || !supabaseAnonKey || supabaseUrl.includes('your_supabase_url_here')\n\nexport const supabase = isDemo ? null : createClient(supabaseUrl!, supabaseAnonKey!)\n\nexport function createSupabaseClient() {\n  if (isDemo) {\n    return null\n  }\n  return createBrowserClient(supabaseUrl!, supabaseAnonKey!)\n}\n\nexport type Database = {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          username: string\n          full_name: string\n          avatar_url: string | null\n          university_group: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          username: string\n          full_name: string\n          avatar_url?: string | null\n          university_group?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          username?: string\n          full_name?: string\n          avatar_url?: string | null\n          university_group?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      works: {\n        Row: {\n          id: string\n          title: string\n          description: string | null\n          subject: string\n          course: number\n          work_type: 'lab' | 'practical' | 'coursework' | 'other'\n          file_url: string\n          file_name: string\n          file_size: number\n          author_id: string\n          rating: number\n          rating_count: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          title: string\n          description?: string | null\n          subject: string\n          course: number\n          work_type: 'lab' | 'practical' | 'coursework' | 'other'\n          file_url: string\n          file_name: string\n          file_size: number\n          author_id: string\n          rating?: number\n          rating_count?: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          title?: string\n          description?: string | null\n          subject?: string\n          course?: number\n          work_type?: 'lab' | 'practical' | 'coursework' | 'other'\n          file_url?: string\n          file_name?: string\n          file_size?: number\n          author_id?: string\n          rating?: number\n          rating_count?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      ratings: {\n        Row: {\n          id: string\n          work_id: string\n          user_id: string\n          rating: number\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          work_id: string\n          user_id: string\n          rating: number\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          work_id?: string\n          user_id?: string\n          rating?: number\n          created_at?: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAGoB;AAHpB;AACA;AAAA;;;AAEA,MAAM,cAAc,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB;AACxD,MAAM,kBAAkB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,6BAA6B;AAEjE,6CAA6C;AAC7C,MAAM,SAAS,CAAC,eAAe,CAAC,mBAAmB,YAAY,QAAQ,CAAC;AAEjE,MAAM,WAAW,SAAS,OAAO,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAc;AAE5D,SAAS;IACd,IAAI,QAAQ;QACV,OAAO;IACT;IACA,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAc;AAC3C", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D0%A0%D0%BE%D0%B1%D0%BE%D1%87%D0%B8%D0%B9%20%D1%81%D1%82%D1%96%D0%BB/ai/knure-works/src/lib/auth.ts"], "sourcesContent": ["import { createSupabaseClient } from './supabase'\n\nexport interface SignUpData {\n  username: string\n  fullName: string\n  password: string\n  confirmPassword: string\n}\n\nexport interface SignInData {\n  username: string\n  password: string\n}\n\n// Generate a unique email for username-based auth\nfunction generateEmailFromUsername(username: string): string {\n  return `${username}@knure-works.local`\n}\n\nexport async function signUpWithUsername(data: SignUpData) {\n  const supabase = createSupabaseClient()\n  \n  if (!supabase) {\n    // Demo mode\n    return {\n      data: {\n        user: {\n          id: 'demo-user-' + Date.now(),\n          email: generateEmailFromUsername(data.username),\n          user_metadata: {\n            username: data.username,\n            full_name: data.fullName\n          }\n        },\n        session: null\n      },\n      error: null\n    }\n  }\n\n  // Check if username already exists\n  const { data: existingProfile } = await supabase\n    .from('profiles')\n    .select('username')\n    .eq('username', data.username)\n    .single()\n\n  if (existingProfile) {\n    return {\n      data: null,\n      error: { message: 'Користувач з таким іменем вже існує' }\n    }\n  }\n\n  // Create user with generated email\n  const email = generateEmailFromUsername(data.username)\n  \n  const { data: authData, error } = await supabase.auth.signUp({\n    email,\n    password: data.password,\n    options: {\n      data: {\n        username: data.username,\n        full_name: data.fullName\n      }\n    }\n  })\n\n  return { data: authData, error }\n}\n\nexport async function signInWithUsername(data: SignInData) {\n  const supabase = createSupabaseClient()\n  \n  if (!supabase) {\n    // Demo mode\n    if (data.username === 'demo' && data.password === 'demo123') {\n      return {\n        data: {\n          user: {\n            id: 'demo-user-id',\n            email: generateEmailFromUsername(data.username),\n            user_metadata: {\n              username: data.username,\n              full_name: 'Демо Користувач'\n            }\n          },\n          session: null\n        },\n        error: null\n      }\n    } else {\n      return {\n        data: null,\n        error: { message: 'Невірне ім\\'я користувача або пароль' }\n      }\n    }\n  }\n\n  // Find user by username\n  const { data: profile } = await supabase\n    .from('profiles')\n    .select('id')\n    .eq('username', data.username)\n    .single()\n\n  if (!profile) {\n    return {\n      data: null,\n      error: { message: 'Користувача з таким іменем не знайдено' }\n    }\n  }\n\n  // Sign in with generated email\n  const email = generateEmailFromUsername(data.username)\n  \n  const { data: authData, error } = await supabase.auth.signInWithPassword({\n    email,\n    password: data.password\n  })\n\n  return { data: authData, error }\n}\n\nexport async function getUserProfile(userId: string) {\n  const supabase = createSupabaseClient()\n  \n  if (!supabase) {\n    return {\n      data: {\n        username: 'demo',\n        full_name: 'Демо Користувач',\n        university_group: null\n      },\n      error: null\n    }\n  }\n\n  const { data, error } = await supabase\n    .from('profiles')\n    .select('username, full_name, university_group')\n    .eq('id', userId)\n    .single()\n\n  return { data, error }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAcA,kDAAkD;AAClD,SAAS,0BAA0B,QAAgB;IACjD,OAAO,AAAC,GAAW,OAAT,UAAS;AACrB;AAEO,eAAe,mBAAmB,IAAgB;IACvD,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,IAAI,CAAC,UAAU;QACb,YAAY;QACZ,OAAO;YACL,MAAM;gBACJ,MAAM;oBACJ,IAAI,eAAe,KAAK,GAAG;oBAC3B,OAAO,0BAA0B,KAAK,QAAQ;oBAC9C,eAAe;wBACb,UAAU,KAAK,QAAQ;wBACvB,WAAW,KAAK,QAAQ;oBAC1B;gBACF;gBACA,SAAS;YACX;YACA,OAAO;QACT;IACF;IAEA,mCAAmC;IACnC,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,YACL,MAAM,CAAC,YACP,EAAE,CAAC,YAAY,KAAK,QAAQ,EAC5B,MAAM;IAET,IAAI,iBAAiB;QACnB,OAAO;YACL,MAAM;YACN,OAAO;gBAAE,SAAS;YAAsC;QAC1D;IACF;IAEA,mCAAmC;IACnC,MAAM,QAAQ,0BAA0B,KAAK,QAAQ;IAErD,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;QAC3D;QACA,UAAU,KAAK,QAAQ;QACvB,SAAS;YACP,MAAM;gBACJ,UAAU,KAAK,QAAQ;gBACvB,WAAW,KAAK,QAAQ;YAC1B;QACF;IACF;IAEA,OAAO;QAAE,MAAM;QAAU;IAAM;AACjC;AAEO,eAAe,mBAAmB,IAAgB;IACvD,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,IAAI,CAAC,UAAU;QACb,YAAY;QACZ,IAAI,KAAK,QAAQ,KAAK,UAAU,KAAK,QAAQ,KAAK,WAAW;YAC3D,OAAO;gBACL,MAAM;oBACJ,MAAM;wBACJ,IAAI;wBACJ,OAAO,0BAA0B,KAAK,QAAQ;wBAC9C,eAAe;4BACb,UAAU,KAAK,QAAQ;4BACvB,WAAW;wBACb;oBACF;oBACA,SAAS;gBACX;gBACA,OAAO;YACT;QACF,OAAO;YACL,OAAO;gBACL,MAAM;gBACN,OAAO;oBAAE,SAAS;gBAAuC;YAC3D;QACF;IACF;IAEA,wBAAwB;IACxB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,YAAY,KAAK,QAAQ,EAC5B,MAAM;IAET,IAAI,CAAC,SAAS;QACZ,OAAO;YACL,MAAM;YACN,OAAO;gBAAE,SAAS;YAAyC;QAC7D;IACF;IAEA,+BAA+B;IAC/B,MAAM,QAAQ,0BAA0B,KAAK,QAAQ;IAErD,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;QACvE;QACA,UAAU,KAAK,QAAQ;IACzB;IAEA,OAAO;QAAE,MAAM;QAAU;IAAM;AACjC;AAEO,eAAe,eAAe,MAAc;IACjD,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,IAAI,CAAC,UAAU;QACb,OAAO;YACL,MAAM;gBACJ,UAAU;gBACV,WAAW;gBACX,kBAAkB;YACpB;YACA,OAAO;QACT;IACF;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,yCACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D0%A0%D0%BE%D0%B1%D0%BE%D1%87%D0%B8%D0%B9%20%D1%81%D1%82%D1%96%D0%BB/ai/knure-works/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { createSupabaseClient } from '@/lib/supabase'\nimport { signUpWithUsername, signInWithUsername, SignUpData, SignInData, getUserProfile } from '@/lib/auth'\n\ninterface UserProfile {\n  username: string\n  full_name: string\n  university_group: string | null\n}\n\ninterface ExtendedUser extends User {\n  profile?: UserProfile\n}\n\nexport function useAuth() {\n  const [user, setUser] = useState<ExtendedUser | null>(null)\n  const [loading, setLoading] = useState(true)\n  const supabase = createSupabaseClient()\n\n  const loadUserProfile = async (authUser: User) => {\n    const { data: profile } = await getUserProfile(authUser.id)\n    return {\n      ...authUser,\n      profile: profile || undefined\n    } as ExtendedUser\n  }\n\n  useEffect(() => {\n    if (!supabase) {\n      // Demo mode - simulate loading\n      setTimeout(() => {\n        setLoading(false)\n      }, 1000)\n      return\n    }\n\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      if (session?.user) {\n        const userWithProfile = await loadUserProfile(session.user)\n        setUser(userWithProfile)\n      } else {\n        setUser(null)\n      }\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (session?.user) {\n          const userWithProfile = await loadUserProfile(session.user)\n          setUser(userWithProfile)\n        } else {\n          setUser(null)\n        }\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [supabase])\n\n  const signIn = async (data: SignInData) => {\n    const result = await signInWithUsername(data)\n    if (result.data?.user && !result.error) {\n      const userWithProfile = await loadUserProfile(result.data.user)\n      setUser(userWithProfile)\n    }\n    return result\n  }\n\n  const signUp = async (data: SignUpData) => {\n    const result = await signUpWithUsername(data)\n    if (result.data?.user && !result.error) {\n      const userWithProfile = await loadUserProfile(result.data.user)\n      setUser(userWithProfile)\n    }\n    return result\n  }\n\n  const signOut = async () => {\n    if (!supabase) {\n      // Demo mode\n      setUser(null)\n      return { error: null }\n    }\n\n    const { error } = await supabase.auth.signOut()\n    return { error }\n  }\n\n  return {\n    user,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAEA;AACA;;AALA;;;;AAiBO,SAAS;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,MAAM,kBAAkB,OAAO;QAC7B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,EAAE;QAC1D,OAAO;YACL,GAAG,QAAQ;YACX,SAAS,WAAW;QACtB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,CAAC,UAAU;gBACb,+BAA+B;gBAC/B;yCAAW;wBACT,WAAW;oBACb;wCAAG;gBACH;YACF;YAEA,sBAAsB;YACtB,MAAM;uDAAoB;oBACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;oBAC5D,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE;wBACjB,MAAM,kBAAkB,MAAM,gBAAgB,QAAQ,IAAI;wBAC1D,QAAQ;oBACV,OAAO;wBACL,QAAQ;oBACV;oBACA,WAAW;gBACb;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;qCAChE,OAAO,OAAO;oBACZ,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE;wBACjB,MAAM,kBAAkB,MAAM,gBAAgB,QAAQ,IAAI;wBAC1D,QAAQ;oBACV,OAAO;wBACL,QAAQ;oBACV;oBACA,WAAW;gBACb;;YAGF;qCAAO,IAAM,aAAa,WAAW;;QACvC;4BAAG;QAAC;KAAS;IAEb,MAAM,SAAS,OAAO;YAEhB;QADJ,MAAM,SAAS,MAAM,CAAA,GAAA,qHAAA,CAAA,qBAAkB,AAAD,EAAE;QACxC,IAAI,EAAA,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,IAAI,KAAI,CAAC,OAAO,KAAK,EAAE;YACtC,MAAM,kBAAkB,MAAM,gBAAgB,OAAO,IAAI,CAAC,IAAI;YAC9D,QAAQ;QACV;QACA,OAAO;IACT;IAEA,MAAM,SAAS,OAAO;YAEhB;QADJ,MAAM,SAAS,MAAM,CAAA,GAAA,qHAAA,CAAA,qBAAkB,AAAD,EAAE;QACxC,IAAI,EAAA,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,IAAI,KAAI,CAAC,OAAO,KAAK,EAAE;YACtC,MAAM,kBAAkB,MAAM,gBAAgB,OAAO,IAAI,CAAC,IAAI;YAC9D,QAAQ;QACV;QACA,OAAO;IACT;IAEA,MAAM,UAAU;QACd,IAAI,CAAC,UAAU;YACb,YAAY;YACZ,QAAQ;YACR,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7C,OAAO;YAAE;QAAM;IACjB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;GAxFgB", "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D0%A0%D0%BE%D0%B1%D0%BE%D1%87%D0%B8%D0%B9%20%D1%81%D1%82%D1%96%D0%BB/ai/knure-works/src/components/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { BookOpen, Upload, LogOut, User, Menu, X } from 'lucide-react'\nimport { useAuth } from '@/hooks/useAuth'\n\ninterface NavbarProps {\n  currentView: 'works' | 'upload' | 'auth'\n  onViewChange: (view: 'works' | 'upload' | 'auth') => void\n}\n\nexport default function Navbar({ currentView, onViewChange }: NavbarProps) {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const { user, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    await signOut()\n    onViewChange('auth')\n    setIsMenuOpen(false)\n  }\n\n  const handleNavClick = (view: 'works' | 'upload' | 'auth') => {\n    onViewChange(view)\n    setIsMenuOpen(false)\n  }\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo and brand */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0 flex items-center\">\n              <BookOpen className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"ml-2 text-xl font-bold text-gray-800\">\n                ХНУРЕ Works\n              </span>\n            </div>\n          </div>\n\n          {/* Desktop navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <button\n              onClick={() => handleNavClick('works')}\n              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                currentView === 'works'\n                  ? 'bg-blue-100 text-blue-700'\n                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n              }`}\n            >\n              Всі роботи\n            </button>\n\n            {user && (\n              <button\n                onClick={() => handleNavClick('upload')}\n                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-1 ${\n                  currentView === 'upload'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                }`}\n              >\n                <Upload className=\"h-4 w-4\" />\n                <span>Завантажити</span>\n              </button>\n            )}\n\n            {user ? (\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n                  <User className=\"h-4 w-4\" />\n                  <span>{user.profile?.username || user.user_metadata?.username || 'Користувач'}</span>\n                </div>\n                <button\n                  onClick={handleSignOut}\n                  className=\"flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors\"\n                >\n                  <LogOut className=\"h-4 w-4\" />\n                  <span>Вийти</span>\n                </button>\n              </div>\n            ) : (\n              <button\n                onClick={() => handleNavClick('auth')}\n                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n                  currentView === 'auth'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-blue-600 text-white hover:bg-blue-700'\n                }`}\n              >\n                Увійти\n              </button>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n            >\n              {isMenuOpen ? (\n                <X className=\"h-6 w-6\" />\n              ) : (\n                <Menu className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200\">\n            <button\n              onClick={() => handleNavClick('works')}\n              className={`block w-full text-left px-3 py-2 rounded-md text-base font-medium transition-colors ${\n                currentView === 'works'\n                  ? 'bg-blue-100 text-blue-700'\n                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n              }`}\n            >\n              Всі роботи\n            </button>\n\n            {user && (\n              <button\n                onClick={() => handleNavClick('upload')}\n                className={`block w-full text-left px-3 py-2 rounded-md text-base font-medium transition-colors ${\n                  currentView === 'upload'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                }`}\n              >\n                <div className=\"flex items-center space-x-2\">\n                  <Upload className=\"h-4 w-4\" />\n                  <span>Завантажити</span>\n                </div>\n              </button>\n            )}\n\n            {user ? (\n              <div className=\"border-t border-gray-200 pt-4 pb-3\">\n                <div className=\"flex items-center px-3 mb-3\">\n                  <User className=\"h-5 w-5 text-gray-400\" />\n                  <span className=\"ml-2 text-sm text-gray-600\">\n                    {user.profile?.username || user.user_metadata?.username || 'Користувач'}\n                  </span>\n                </div>\n                <button\n                  onClick={handleSignOut}\n                  className=\"block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors\"\n                >\n                  <div className=\"flex items-center space-x-2\">\n                    <LogOut className=\"h-4 w-4\" />\n                    <span>Вийти</span>\n                  </div>\n                </button>\n              </div>\n            ) : (\n              <button\n                onClick={() => handleNavClick('auth')}\n                className=\"block w-full text-left px-3 py-2 rounded-md text-base font-medium bg-blue-600 text-white hover:bg-blue-700 transition-colors\"\n              >\n                Увійти\n              </button>\n            )}\n          </div>\n        </div>\n      )}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAWe,SAAS,OAAO,KAA0C;QAA1C,EAAE,WAAW,EAAE,YAAY,EAAe,GAA1C;QA4DN,eAA0B,qBA4E9B,gBAA0B;;IAvI7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,gBAAgB;QACpB,MAAM;QACN,aAAa;QACb,cAAc;IAChB;IAEA,MAAM,iBAAiB,CAAC;QACtB,aAAa;QACb,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAuC;;;;;;;;;;;;;;;;;sCAO3D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAW,AAAC,8DAIX,OAHC,gBAAgB,UACZ,8BACA;8CAEP;;;;;;gCAIA,sBACC,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAW,AAAC,0FAIX,OAHC,gBAAgB,WACZ,8BACA;;sDAGN,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAK;;;;;;;;;;;;gCAIT,qBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAM,EAAA,gBAAA,KAAK,OAAO,cAAZ,oCAAA,cAAc,QAAQ,OAAI,sBAAA,KAAK,aAAa,cAAlB,0CAAA,oBAAoB,QAAQ,KAAI;;;;;;;;;;;;sDAEnE,6LAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;yDAIV,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAW,AAAC,8DAIX,OAHC,gBAAgB,SACZ,2BACA;8CAEP;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAET,2BACC,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAEb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQzB,4BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAW,AAAC,uFAIX,OAHC,gBAAgB,UACZ,8BACA;sCAEP;;;;;;wBAIA,sBACC,6LAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAW,AAAC,uFAIX,OAHC,gBAAgB,WACZ,8BACA;sCAGN,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;wBAKX,qBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAK,WAAU;sDACb,EAAA,iBAAA,KAAK,OAAO,cAAZ,qCAAA,eAAc,QAAQ,OAAI,uBAAA,KAAK,aAAa,cAAlB,2CAAA,qBAAoB,QAAQ,KAAI;;;;;;;;;;;;8CAG/D,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;iDAKZ,6LAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AASf;GAlKwB;;QAEI,0HAAA,CAAA,UAAO;;;KAFX", "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D0%A0%D0%BE%D0%B1%D0%BE%D1%87%D0%B8%D0%B9%20%D1%81%D1%82%D1%96%D0%BB/ai/knure-works/src/components/AuthForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { useAuth } from '@/hooks/useAuth'\n\nconst authSchema = z.object({\n  username: z.string()\n    .min(3, 'Ім\\'я користувача повинно містити мінімум 3 символи')\n    .max(20, 'Ім\\'я користувача не може містити більше 20 символів')\n    .regex(/^[a-zA-Z0-9_]+$/, 'Ім\\'я користувача може містити тільки літери, цифри та підкреслення'),\n  password: z.string().min(6, 'Пароль повинен містити мінімум 6 символів'),\n  confirmPassword: z.string().optional(),\n  fullName: z.string().min(2, 'Повне ім\\'я повинно містити мінімум 2 символи').optional(),\n}).refine((data) => {\n  if (data.confirmPassword !== undefined) {\n    return data.password === data.confirmPassword\n  }\n  return true\n}, {\n  message: 'Паролі не співпадають',\n  path: ['confirmPassword']\n})\n\ntype AuthFormData = z.infer<typeof authSchema>\n\ninterface AuthFormProps {\n  onSuccess?: () => void\n}\n\nexport default function AuthForm({ onSuccess }: AuthFormProps) {\n  const [isSignUp, setIsSignUp] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const { signIn, signUp } = useAuth()\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n  } = useForm<AuthFormData>({\n    resolver: zodResolver(authSchema),\n  })\n\n  const onSubmit = async (data: AuthFormData) => {\n    setLoading(true)\n    setError(null)\n\n    try {\n      if (isSignUp) {\n        if (!data.fullName) {\n          setError('Повне ім\\'я є обов\\'язковим для реєстрації')\n          return\n        }\n\n        const signUpData = {\n          username: data.username,\n          fullName: data.fullName,\n          password: data.password,\n          confirmPassword: data.confirmPassword || data.password\n        }\n\n        const { error } = await signUp(signUpData)\n        if (error) {\n          setError(error.message)\n        } else {\n          setError(null)\n          onSuccess?.()\n          alert('Реєстрація успішна! Ви увійшли в систему.')\n        }\n      } else {\n        const signInData = {\n          username: data.username,\n          password: data.password\n        }\n\n        const { error } = await signIn(signInData)\n        if (error) {\n          setError(error.message)\n        } else {\n          onSuccess?.()\n        }\n      }\n    } catch (err) {\n      setError('Сталася помилка. Спробуйте ще раз.')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const toggleMode = () => {\n    setIsSignUp(!isSignUp)\n    setError(null)\n    reset()\n  }\n\n  return (\n    <div className=\"max-w-md mx-auto bg-white p-8 rounded-lg shadow-md\">\n      <h2 className=\"text-2xl font-bold text-center mb-6 text-gray-800\">\n        {isSignUp ? 'Реєстрація' : 'Вхід'}\n      </h2>\n\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n        {isSignUp && (\n          <div>\n            <label htmlFor=\"fullName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Повне ім'я *\n            </label>\n            <input\n              {...register('fullName')}\n              type=\"text\"\n              id=\"fullName\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"Введіть ваше повне ім'я\"\n            />\n            {errors.fullName && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.fullName.message}</p>\n            )}\n          </div>\n        )}\n\n        <div>\n          <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Ім'я користувача *\n          </label>\n          <input\n            {...register('username')}\n            type=\"text\"\n            id=\"username\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            placeholder=\"Введіть ім'я користувача\"\n          />\n          {errors.username && (\n            <p className=\"text-red-500 text-sm mt-1\">{errors.username.message}</p>\n          )}\n          <p className=\"text-xs text-gray-500 mt-1\">\n            Тільки літери, цифри та підкреслення. 3-20 символів.\n          </p>\n        </div>\n\n        <div>\n          <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Пароль *\n          </label>\n          <input\n            {...register('password')}\n            type=\"password\"\n            id=\"password\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            placeholder=\"Введіть ваш пароль\"\n          />\n          {errors.password && (\n            <p className=\"text-red-500 text-sm mt-1\">{errors.password.message}</p>\n          )}\n        </div>\n\n        {isSignUp && (\n          <div>\n            <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Підтвердження паролю *\n            </label>\n            <input\n              {...register('confirmPassword')}\n              type=\"password\"\n              id=\"confirmPassword\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"Підтвердіть ваш пароль\"\n            />\n            {errors.confirmPassword && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.confirmPassword.message}</p>\n            )}\n          </div>\n        )}\n\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\">\n            {error}\n          </div>\n        )}\n\n        <button\n          type=\"submit\"\n          disabled={loading}\n          className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {loading ? 'Завантаження...' : (isSignUp ? 'Зареєструватися' : 'Увійти')}\n        </button>\n      </form>\n\n      <div className=\"mt-6 text-center\">\n        <button\n          onClick={toggleMode}\n          className=\"text-blue-600 hover:text-blue-800 text-sm\"\n        >\n          {isSignUp \n            ? 'Вже маєте акаунт? Увійти' \n            : 'Немає акаунту? Зареєструватися'\n          }\n        </button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GACf,GAAG,CAAC,GAAG,uDACP,GAAG,CAAC,IAAI,wDACR,KAAK,CAAC,mBAAmB;IAC5B,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,iBAAiB,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACpC,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,iDAAiD,QAAQ;AACvF,GAAG,MAAM,CAAC,CAAC;IACT,IAAI,KAAK,eAAe,KAAK,WAAW;QACtC,OAAO,KAAK,QAAQ,KAAK,KAAK,eAAe;IAC/C;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAQe,SAAS,SAAS,KAA4B;QAA5B,EAAE,SAAS,EAAiB,GAA5B;;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAEjC,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAgB;QACxB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,WAAW;QACX,SAAS;QAET,IAAI;YACF,IAAI,UAAU;gBACZ,IAAI,CAAC,KAAK,QAAQ,EAAE;oBAClB,SAAS;oBACT;gBACF;gBAEA,MAAM,aAAa;oBACjB,UAAU,KAAK,QAAQ;oBACvB,UAAU,KAAK,QAAQ;oBACvB,UAAU,KAAK,QAAQ;oBACvB,iBAAiB,KAAK,eAAe,IAAI,KAAK,QAAQ;gBACxD;gBAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO;gBAC/B,IAAI,OAAO;oBACT,SAAS,MAAM,OAAO;gBACxB,OAAO;oBACL,SAAS;oBACT,sBAAA,gCAAA;oBACA,MAAM;gBACR;YACF,OAAO;gBACL,MAAM,aAAa;oBACjB,UAAU,KAAK,QAAQ;oBACvB,UAAU,KAAK,QAAQ;gBACzB;gBAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO;gBAC/B,IAAI,OAAO;oBACT,SAAS,MAAM,OAAO;gBACxB,OAAO;oBACL,sBAAA,gCAAA;gBACF;YACF;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,YAAY,CAAC;QACb,SAAS;QACT;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BACX,WAAW,eAAe;;;;;;0BAG7B,6LAAC;gBAAK,UAAU,aAAa;gBAAW,WAAU;;oBAC/C,0BACC,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA+C;;;;;;0CAGnF,6LAAC;gCACE,GAAG,SAAS,WAAW;gCACxB,MAAK;gCACL,IAAG;gCACH,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,QAAQ,kBACd,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;kCAKvE,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA+C;;;;;;0CAGnF,6LAAC;gCACE,GAAG,SAAS,WAAW;gCACxB,MAAK;gCACL,IAAG;gCACH,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,QAAQ,kBACd,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;0CAEnE,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAK5C,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA+C;;;;;;0CAGnF,6LAAC;gCACE,GAAG,SAAS,WAAW;gCACxB,MAAK;gCACL,IAAG;gCACH,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,QAAQ,kBACd,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;oBAIpE,0BACC,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAkB,WAAU;0CAA+C;;;;;;0CAG1F,6LAAC;gCACE,GAAG,SAAS,kBAAkB;gCAC/B,MAAK;gCACL,IAAG;gCACH,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,eAAe,kBACrB,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,eAAe,CAAC,OAAO;;;;;;;;;;;;oBAK7E,uBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIL,6LAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAU;kCAET,UAAU,oBAAqB,WAAW,oBAAoB;;;;;;;;;;;;0BAInE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAET,WACG,6BACA;;;;;;;;;;;;;;;;;AAMd;GA7KwB;;QAIK,0HAAA,CAAA,UAAO;QAO9B,iKAAA,CAAA,UAAO;;;KAXW", "debugId": null}}, {"offset": {"line": 976, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D0%A0%D0%BE%D0%B1%D0%BE%D1%87%D0%B8%D0%B9%20%D1%81%D1%82%D1%96%D0%BB/ai/knure-works/src/components/UploadForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { Upload, FileText } from 'lucide-react'\nimport { createSupabaseClient } from '@/lib/supabase'\nimport { useAuth } from '@/hooks/useAuth'\n\nconst uploadSchema = z.object({\n  title: z.string().min(3, 'Назва повинна містити мінімум 3 символи'),\n  description: z.string().optional(),\n  subject: z.string().min(2, 'Предмет повинен містити мінімум 2 символи'),\n  course: z.number().min(1).max(6),\n  workType: z.enum(['lab', 'practical', 'coursework', 'other']),\n})\n\ntype UploadFormData = z.infer<typeof uploadSchema>\n\ninterface UploadFormProps {\n  onSuccess?: () => void\n}\n\nexport default function UploadForm({ onSuccess }: UploadFormProps) {\n  const [file, setFile] = useState<File | null>(null)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const { user } = useAuth()\n  const supabase = createSupabaseClient()\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n  } = useForm<UploadFormData>({\n    resolver: zodResolver(uploadSchema),\n  })\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFile = e.target.files?.[0]\n    if (selectedFile) {\n      // Check file size (max 10MB)\n      if (selectedFile.size > 10 * 1024 * 1024) {\n        setError('Файл занадто великий. Максимальний розмір: 10MB')\n        return\n      }\n      setFile(selectedFile)\n      setError(null)\n    }\n  }\n\n  const onSubmit = async (data: UploadFormData) => {\n    if (!file) {\n      setError('Будь ласка, оберіть файл')\n      return\n    }\n\n    if (!user) {\n      setError('Ви повинні увійти в систему')\n      return\n    }\n\n    setLoading(true)\n    setError(null)\n\n    try {\n      if (!supabase) {\n        // Demo mode - simulate upload\n        await new Promise(resolve => setTimeout(resolve, 2000))\n\n        // Save to localStorage for demo\n        const works = JSON.parse(localStorage.getItem('demo-works') || '[]')\n        const newWork = {\n          id: Date.now().toString(),\n          title: data.title,\n          description: data.description || null,\n          subject: data.subject,\n          course: data.course,\n          work_type: data.workType,\n          file_url: '#demo-file',\n          file_name: file.name,\n          file_size: file.size,\n          author_id: user.id,\n          rating: 0,\n          rating_count: 0,\n          created_at: new Date().toISOString(),\n          profiles: {\n            full_name: user.user_metadata?.full_name || 'Демо Користувач'\n          }\n        }\n        works.push(newWork)\n        localStorage.setItem('demo-works', JSON.stringify(works))\n\n        reset()\n        setFile(null)\n        onSuccess?.()\n        alert('Роботу успішно завантажено! (Демо режим)')\n        return\n      }\n\n      // Upload file to Supabase Storage\n      const fileExt = file.name.split('.').pop()\n      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`\n      const filePath = `works/${fileName}`\n\n      const { error: uploadError } = await supabase.storage\n        .from('files')\n        .upload(filePath, file)\n\n      if (uploadError) {\n        throw uploadError\n      }\n\n      // Get public URL\n      const { data: { publicUrl } } = supabase.storage\n        .from('files')\n        .getPublicUrl(filePath)\n\n      // Save work info to database\n      const { error: dbError } = await supabase\n        .from('works')\n        .insert({\n          title: data.title,\n          description: data.description || null,\n          subject: data.subject,\n          course: data.course,\n          work_type: data.workType,\n          file_url: publicUrl,\n          file_name: file.name,\n          file_size: file.size,\n          author_id: user.id,\n        })\n\n      if (dbError) {\n        throw dbError\n      }\n\n      reset()\n      setFile(null)\n      onSuccess?.()\n      alert('Роботу успішно завантажено!')\n    } catch (err: any) {\n      setError(err.message || 'Сталася помилка при завантаженні')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const workTypeOptions = [\n    { value: 'lab', label: 'Лабораторна робота' },\n    { value: 'practical', label: 'Практична робота' },\n    { value: 'coursework', label: 'Курсова робота' },\n    { value: 'other', label: 'Інше' },\n  ]\n\n  return (\n    <div className=\"max-w-2xl mx-auto bg-white p-8 rounded-lg shadow-md\">\n      <h2 className=\"text-2xl font-bold text-center mb-6 text-gray-800\">\n        Завантажити роботу\n      </h2>\n\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n        <div>\n          <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Назва роботи *\n          </label>\n          <input\n            {...register('title')}\n            type=\"text\"\n            id=\"title\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            placeholder=\"Наприклад: Лабораторна робота №1 з програмування\"\n          />\n          {errors.title && (\n            <p className=\"text-red-500 text-sm mt-1\">{errors.title.message}</p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Опис роботи\n          </label>\n          <textarea\n            {...register('description')}\n            id=\"description\"\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            placeholder=\"Короткий опис роботи, що вона включає...\"\n          />\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label htmlFor=\"subject\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Предмет *\n            </label>\n            <input\n              {...register('subject')}\n              type=\"text\"\n              id=\"subject\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"Наприклад: Програмування\"\n            />\n            {errors.subject && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.subject.message}</p>\n            )}\n          </div>\n\n          <div>\n            <label htmlFor=\"course\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Курс *\n            </label>\n            <select\n              {...register('course', { valueAsNumber: true })}\n              id=\"course\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">Оберіть курс</option>\n              {[1, 2, 3, 4, 5, 6].map(course => (\n                <option key={course} value={course}>{course} курс</option>\n              ))}\n            </select>\n            {errors.course && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.course.message}</p>\n            )}\n          </div>\n        </div>\n\n        <div>\n          <label htmlFor=\"workType\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Тип роботи *\n          </label>\n          <select\n            {...register('workType')}\n            id=\"workType\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <option value=\"\">Оберіть тип роботи</option>\n            {workTypeOptions.map(option => (\n              <option key={option.value} value={option.value}>\n                {option.label}\n              </option>\n            ))}\n          </select>\n          {errors.workType && (\n            <p className=\"text-red-500 text-sm mt-1\">{errors.workType.message}</p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"file\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Файл роботи *\n          </label>\n          <div className=\"mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors\">\n            <div className=\"space-y-1 text-center\">\n              {file ? (\n                <div className=\"flex items-center justify-center space-x-2\">\n                  <FileText className=\"h-8 w-8 text-blue-500\" />\n                  <span className=\"text-sm text-gray-600\">{file.name}</span>\n                </div>\n              ) : (\n                <Upload className=\"mx-auto h-12 w-12 text-gray-400\" />\n              )}\n              <div className=\"flex text-sm text-gray-600\">\n                <label\n                  htmlFor=\"file\"\n                  className=\"relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500\"\n                >\n                  <span>{file ? 'Змінити файл' : 'Завантажити файл'}</span>\n                  <input\n                    id=\"file\"\n                    name=\"file\"\n                    type=\"file\"\n                    className=\"sr-only\"\n                    onChange={handleFileChange}\n                    accept=\".pdf,.doc,.docx,.txt,.zip,.rar\"\n                  />\n                </label>\n              </div>\n              <p className=\"text-xs text-gray-500\">\n                PDF, DOC, DOCX, TXT, ZIP, RAR до 10MB\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\">\n            {error}\n          </div>\n        )}\n\n        <button\n          type=\"submit\"\n          disabled={loading || !file}\n          className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\"\n        >\n          {loading ? (\n            <span>Завантаження...</span>\n          ) : (\n            <>\n              <Upload className=\"h-5 w-5\" />\n              <span>Завантажити роботу</span>\n            </>\n          )}\n        </button>\n      </form>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AARA;;;;;;;;AAUA,MAAM,eAAe,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,QAAQ,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC9B,UAAU,gLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;QAAa;QAAc;KAAQ;AAC9D;AAQe,SAAS,WAAW,KAA8B;QAA9B,EAAE,SAAS,EAAmB,GAA9B;;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAkB;QAC1B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,mBAAmB,CAAC;YACH;QAArB,MAAM,gBAAe,kBAAA,EAAE,MAAM,CAAC,KAAK,cAAd,sCAAA,eAAgB,CAAC,EAAE;QACxC,IAAI,cAAc;YAChB,6BAA6B;YAC7B,IAAI,aAAa,IAAI,GAAG,KAAK,OAAO,MAAM;gBACxC,SAAS;gBACT;YACF;YACA,QAAQ;YACR,SAAS;QACX;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,MAAM;YACT,SAAS;YACT;QACF;QAEA,IAAI,CAAC,MAAM;YACT,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,IAAI,CAAC,UAAU;oBAqBE;gBApBf,8BAA8B;gBAC9B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,gCAAgC;gBAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,iBAAiB;gBAC/D,MAAM,UAAU;oBACd,IAAI,KAAK,GAAG,GAAG,QAAQ;oBACvB,OAAO,KAAK,KAAK;oBACjB,aAAa,KAAK,WAAW,IAAI;oBACjC,SAAS,KAAK,OAAO;oBACrB,QAAQ,KAAK,MAAM;oBACnB,WAAW,KAAK,QAAQ;oBACxB,UAAU;oBACV,WAAW,KAAK,IAAI;oBACpB,WAAW,KAAK,IAAI;oBACpB,WAAW,KAAK,EAAE;oBAClB,QAAQ;oBACR,cAAc;oBACd,YAAY,IAAI,OAAO,WAAW;oBAClC,UAAU;wBACR,WAAW,EAAA,sBAAA,KAAK,aAAa,cAAlB,0CAAA,oBAAoB,SAAS,KAAI;oBAC9C;gBACF;gBACA,MAAM,IAAI,CAAC;gBACX,aAAa,OAAO,CAAC,cAAc,KAAK,SAAS,CAAC;gBAElD;gBACA,QAAQ;gBACR,sBAAA,gCAAA;gBACA,MAAM;gBACN;YACF;YAEA,kCAAkC;YAClC,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;YACxC,MAAM,WAAW,AAAC,GAAgB,OAAd,KAAK,GAAG,IAAG,KAA8C,OAA3C,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,IAAG,KAAW,OAAR;YAC7E,MAAM,WAAW,AAAC,SAAiB,OAAT;YAE1B,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAAS,OAAO,CAClD,IAAI,CAAC,SACL,MAAM,CAAC,UAAU;YAEpB,IAAI,aAAa;gBACf,MAAM;YACR;YAEA,iBAAiB;YACjB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,GAAG,SAAS,OAAO,CAC7C,IAAI,CAAC,SACL,YAAY,CAAC;YAEhB,6BAA6B;YAC7B,MAAM,EAAE,OAAO,OAAO,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,OAAO,KAAK,KAAK;gBACjB,aAAa,KAAK,WAAW,IAAI;gBACjC,SAAS,KAAK,OAAO;gBACrB,QAAQ,KAAK,MAAM;gBACnB,WAAW,KAAK,QAAQ;gBACxB,UAAU;gBACV,WAAW,KAAK,IAAI;gBACpB,WAAW,KAAK,IAAI;gBACpB,WAAW,KAAK,EAAE;YACpB;YAEF,IAAI,SAAS;gBACX,MAAM;YACR;YAEA;YACA,QAAQ;YACR,sBAAA,gCAAA;YACA,MAAM;QACR,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAO,OAAO;QAAqB;QAC5C;YAAE,OAAO;YAAa,OAAO;QAAmB;QAChD;YAAE,OAAO;YAAc,OAAO;QAAiB;QAC/C;YAAE,OAAO;YAAS,OAAO;QAAO;KACjC;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAoD;;;;;;0BAIlE,6LAAC;gBAAK,UAAU,aAAa;gBAAW,WAAU;;kCAChD,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA+C;;;;;;0CAGhF,6LAAC;gCACE,GAAG,SAAS,QAAQ;gCACrB,MAAK;gCACL,IAAG;gCACH,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,KAAK,kBACX,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAIlE,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAc,WAAU;0CAA+C;;;;;;0CAGtF,6LAAC;gCACE,GAAG,SAAS,cAAc;gCAC3B,IAAG;gCACH,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAIhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,SAAQ;wCAAU,WAAU;kDAA+C;;;;;;kDAGlF,6LAAC;wCACE,GAAG,SAAS,UAAU;wCACvB,MAAK;wCACL,IAAG;wCACH,WAAU;wCACV,aAAY;;;;;;oCAEb,OAAO,OAAO,kBACb,6LAAC;wCAAE,WAAU;kDAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;0CAIpE,6LAAC;;kDACC,6LAAC;wCAAM,SAAQ;wCAAS,WAAU;kDAA+C;;;;;;kDAGjF,6LAAC;wCACE,GAAG,SAAS,UAAU;4CAAE,eAAe;wCAAK,EAAE;wCAC/C,IAAG;wCACH,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB;gDAAC;gDAAG;gDAAG;gDAAG;gDAAG;gDAAG;6CAAE,CAAC,GAAG,CAAC,CAAA,uBACtB,6LAAC;oDAAoB,OAAO;;wDAAS;wDAAO;;mDAA/B;;;;;;;;;;;oCAGhB,OAAO,MAAM,kBACZ,6LAAC;wCAAE,WAAU;kDAA6B,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;;;;;;;kCAKrE,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA+C;;;;;;0CAGnF,6LAAC;gCACE,GAAG,SAAS,WAAW;gCACxB,IAAG;gCACH,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,gBAAgB,GAAG,CAAC,CAAA,uBACnB,6LAAC;4CAA0B,OAAO,OAAO,KAAK;sDAC3C,OAAO,KAAK;2CADF,OAAO,KAAK;;;;;;;;;;;4BAK5B,OAAO,QAAQ,kBACd,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;kCAIrE,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAA+C;;;;;;0CAG/E,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;wCACZ,qBACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAK,WAAU;8DAAyB,KAAK,IAAI;;;;;;;;;;;iEAGpD,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAEpB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,SAAQ;gDACR,WAAU;;kEAEV,6LAAC;kEAAM,OAAO,iBAAiB;;;;;;kEAC/B,6LAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,WAAU;wDACV,UAAU;wDACV,QAAO;;;;;;;;;;;;;;;;;sDAIb,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;oBAO1C,uBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIL,6LAAC;wBACC,MAAK;wBACL,UAAU,WAAW,CAAC;wBACtB,WAAU;kCAET,wBACC,6LAAC;sCAAK;;;;;iDAEN;;8CACE,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;GA/RwB;;QAIL,0HAAA,CAAA,UAAO;QAQpB,iKAAA,CAAA,UAAO;;;KAZW", "debugId": null}}, {"offset": {"line": 1550, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D0%A0%D0%BE%D0%B1%D0%BE%D1%87%D0%B8%D0%B9%20%D1%81%D1%82%D1%96%D0%BB/ai/knure-works/src/components/WorksList.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Star, Download, Search, Filter } from 'lucide-react'\nimport { createSupabaseClient } from '@/lib/supabase'\nimport { useAuth } from '@/hooks/useAuth'\n\ninterface Work {\n  id: string\n  title: string\n  description: string | null\n  subject: string\n  course: number\n  work_type: 'lab' | 'practical' | 'coursework' | 'other'\n  file_url: string\n  file_name: string\n  file_size: number\n  author_id: string\n  rating: number\n  rating_count: number\n  created_at: string\n  profiles?: {\n    full_name: string | null\n  }\n}\n\nexport default function WorksList() {\n  const [works, setWorks] = useState<Work[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedCourse, setSelectedCourse] = useState<number | ''>('')\n  const [selectedType, setSelectedType] = useState<string>('')\n  const { user } = useAuth()\n  const supabase = createSupabaseClient()\n\n  useEffect(() => {\n    fetchWorks()\n  }, [])\n\n  const fetchWorks = async () => {\n    try {\n      if (!supabase) {\n        // Demo mode - load from localStorage and add some sample data\n        const savedWorks = JSON.parse(localStorage.getItem('demo-works') || '[]')\n        const sampleWorks = [\n          {\n            id: 'sample-1',\n            title: 'Лабораторна робота №1 з програмування',\n            description: 'Основи роботи з масивами та циклами',\n            subject: 'Програмування',\n            course: 1,\n            work_type: 'lab',\n            file_url: '#demo-file',\n            file_name: 'lab1_programming.pdf',\n            file_size: 1024000,\n            author_id: 'sample-user',\n            rating: 4.5,\n            rating_count: 12,\n            created_at: '2024-01-15T10:00:00Z',\n            profiles: { full_name: 'Іван Петренко' }\n          },\n          {\n            id: 'sample-2',\n            title: 'Практична робота з математики',\n            description: 'Розв\\'язання диференціальних рівнянь',\n            subject: 'Математика',\n            course: 2,\n            work_type: 'practical',\n            file_url: '#demo-file',\n            file_name: 'math_practical.docx',\n            file_size: 512000,\n            author_id: 'sample-user-2',\n            rating: 4.2,\n            rating_count: 8,\n            created_at: '2024-01-10T14:30:00Z',\n            profiles: { full_name: 'Марія Коваленко' }\n          },\n          {\n            id: 'sample-3',\n            title: 'Курсова робота з фізики',\n            description: 'Дослідження електромагнітних хвиль',\n            subject: 'Фізика',\n            course: 3,\n            work_type: 'coursework',\n            file_url: '#demo-file',\n            file_name: 'physics_coursework.pdf',\n            file_size: 2048000,\n            author_id: 'sample-user-3',\n            rating: 4.8,\n            rating_count: 15,\n            created_at: '2024-01-05T09:15:00Z',\n            profiles: { full_name: 'Олександр Сидоренко' }\n          }\n        ]\n\n        const allWorks = [...sampleWorks, ...savedWorks]\n        setWorks(allWorks)\n        setLoading(false)\n        return\n      }\n\n      const { data, error } = await supabase\n        .from('works')\n        .select(`\n          *,\n          profiles (\n            full_name\n          )\n        `)\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n      setWorks(data || [])\n    } catch (error) {\n      console.error('Error fetching works:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleRating = async (workId: string, rating: number) => {\n    if (!user) {\n      alert('Ви повинні увійти в систему для оцінювання')\n      return\n    }\n\n    try {\n      if (!supabase) {\n        // Demo mode - update localStorage\n        const allWorks = [...works]\n        const workIndex = allWorks.findIndex(w => w.id === workId)\n        if (workIndex !== -1) {\n          const work = allWorks[workIndex]\n          const newRatingCount = work.rating_count + 1\n          const newRating = ((work.rating * work.rating_count) + rating) / newRatingCount\n\n          allWorks[workIndex] = {\n            ...work,\n            rating: newRating,\n            rating_count: newRatingCount\n          }\n\n          setWorks(allWorks)\n\n          // Update localStorage if it's a user-created work\n          const savedWorks = JSON.parse(localStorage.getItem('demo-works') || '[]')\n          const savedWorkIndex = savedWorks.findIndex((w: any) => w.id === workId)\n          if (savedWorkIndex !== -1) {\n            savedWorks[savedWorkIndex] = allWorks[workIndex]\n            localStorage.setItem('demo-works', JSON.stringify(savedWorks))\n          }\n        }\n        return\n      }\n\n      // Check if user already rated this work\n      const { data: existingRating } = await supabase\n        .from('ratings')\n        .select('*')\n        .eq('work_id', workId)\n        .eq('user_id', user.id)\n        .single()\n\n      if (existingRating) {\n        // Update existing rating\n        await supabase\n          .from('ratings')\n          .update({ rating })\n          .eq('id', existingRating.id)\n      } else {\n        // Create new rating\n        await supabase\n          .from('ratings')\n          .insert({\n            work_id: workId,\n            user_id: user.id,\n            rating,\n          })\n      }\n\n      // Recalculate work rating\n      const { data: ratings } = await supabase\n        .from('ratings')\n        .select('rating')\n        .eq('work_id', workId)\n\n      if (ratings) {\n        const avgRating = ratings.reduce((sum, r) => sum + r.rating, 0) / ratings.length\n        const ratingCount = ratings.length\n\n        await supabase\n          .from('works')\n          .update({\n            rating: avgRating,\n            rating_count: ratingCount,\n          })\n          .eq('id', workId)\n\n        // Refresh works list\n        fetchWorks()\n      }\n    } catch (error) {\n      console.error('Error rating work:', error)\n      alert('Помилка при оцінюванні роботи')\n    }\n  }\n\n  const filteredWorks = works.filter(work => {\n    const matchesSearch = work.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         work.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         work.description?.toLowerCase().includes(searchTerm.toLowerCase())\n    \n    const matchesCourse = selectedCourse === '' || work.course === selectedCourse\n    const matchesType = selectedType === '' || work.work_type === selectedType\n\n    return matchesSearch && matchesCourse && matchesType\n  })\n\n  const getWorkTypeLabel = (type: string) => {\n    const types = {\n      lab: 'Лабораторна',\n      practical: 'Практична',\n      coursework: 'Курсова',\n      other: 'Інше',\n    }\n    return types[type as keyof typeof types] || type\n  }\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes'\n    const k = 1024\n    const sizes = ['Bytes', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('uk-UA')\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center py-12\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto\">\n      {/* Search and Filters */}\n      <div className=\"bg-white p-6 rounded-lg shadow-md mb-6\">\n        <div className=\"flex flex-col md:flex-row gap-4\">\n          <div className=\"flex-1\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\" />\n              <input\n                type=\"text\"\n                placeholder=\"Пошук робіт...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n          </div>\n          \n          <div className=\"flex gap-4\">\n            <select\n              value={selectedCourse}\n              onChange={(e) => setSelectedCourse(e.target.value === '' ? '' : Number(e.target.value))}\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">Всі курси</option>\n              {[1, 2, 3, 4, 5, 6].map(course => (\n                <option key={course} value={course}>{course} курс</option>\n              ))}\n            </select>\n\n            <select\n              value={selectedType}\n              onChange={(e) => setSelectedType(e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">Всі типи</option>\n              <option value=\"lab\">Лабораторні</option>\n              <option value=\"practical\">Практичні</option>\n              <option value=\"coursework\">Курсові</option>\n              <option value=\"other\">Інше</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Works Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {filteredWorks.map((work) => (\n          <div key={work.id} className=\"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow\">\n            <div className=\"flex justify-between items-start mb-3\">\n              <span className=\"inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\">\n                {getWorkTypeLabel(work.work_type)}\n              </span>\n              <span className=\"text-sm text-gray-500\">{work.course} курс</span>\n            </div>\n\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-2 line-clamp-2\">\n              {work.title}\n            </h3>\n\n            <p className=\"text-sm text-gray-600 mb-2\">\n              <strong>Предмет:</strong> {work.subject}\n            </p>\n\n            {work.description && (\n              <p className=\"text-sm text-gray-600 mb-3 line-clamp-2\">\n                {work.description}\n              </p>\n            )}\n\n            <div className=\"flex items-center justify-between mb-3\">\n              <div className=\"flex items-center space-x-1\">\n                {[1, 2, 3, 4, 5].map((star) => (\n                  <button\n                    key={star}\n                    onClick={() => handleRating(work.id, star)}\n                    className=\"focus:outline-none\"\n                  >\n                    <Star\n                      className={`h-4 w-4 ${\n                        star <= work.rating\n                          ? 'text-yellow-400 fill-current'\n                          : 'text-gray-300'\n                      } hover:text-yellow-400 transition-colors`}\n                    />\n                  </button>\n                ))}\n                <span className=\"text-sm text-gray-500 ml-2\">\n                  ({work.rating_count})\n                </span>\n              </div>\n            </div>\n\n            <div className=\"text-xs text-gray-500 mb-3\">\n              <p>Автор: {work.profiles?.full_name || 'Анонім'}</p>\n              <p>Розмір: {formatFileSize(work.file_size)}</p>\n              <p>Дата: {formatDate(work.created_at)}</p>\n            </div>\n\n            <a\n              href={work.file_url}\n              download={work.file_name}\n              className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2\"\n            >\n              <Download className=\"h-4 w-4\" />\n              <span>Завантажити</span>\n            </a>\n          </div>\n        ))}\n      </div>\n\n      {filteredWorks.length === 0 && (\n        <div className=\"text-center py-12\">\n          <p className=\"text-gray-500 text-lg\">Роботи не знайдено</p>\n          <p className=\"text-gray-400 text-sm mt-2\">\n            Спробуйте змінити параметри пошуку або завантажте першу роботу!\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AA0Be,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,IAAI,CAAC,UAAU;gBACb,8DAA8D;gBAC9D,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,iBAAiB;gBACpE,MAAM,cAAc;oBAClB;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,SAAS;wBACT,QAAQ;wBACR,WAAW;wBACX,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,QAAQ;wBACR,cAAc;wBACd,YAAY;wBACZ,UAAU;4BAAE,WAAW;wBAAgB;oBACzC;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,SAAS;wBACT,QAAQ;wBACR,WAAW;wBACX,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,QAAQ;wBACR,cAAc;wBACd,YAAY;wBACZ,UAAU;4BAAE,WAAW;wBAAkB;oBAC3C;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,SAAS;wBACT,QAAQ;wBACR,WAAW;wBACX,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,QAAQ;wBACR,cAAc;wBACd,YAAY;wBACZ,UAAU;4BAAE,WAAW;wBAAsB;oBAC/C;iBACD;gBAED,MAAM,WAAW;uBAAI;uBAAgB;iBAAW;gBAChD,SAAS;gBACT,WAAW;gBACX;YACF;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAE,sFAMR,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YACjB,SAAS,QAAQ,EAAE;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO,QAAgB;QAC1C,IAAI,CAAC,MAAM;YACT,MAAM;YACN;QACF;QAEA,IAAI;YACF,IAAI,CAAC,UAAU;gBACb,kCAAkC;gBAClC,MAAM,WAAW;uBAAI;iBAAM;gBAC3B,MAAM,YAAY,SAAS,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACnD,IAAI,cAAc,CAAC,GAAG;oBACpB,MAAM,OAAO,QAAQ,CAAC,UAAU;oBAChC,MAAM,iBAAiB,KAAK,YAAY,GAAG;oBAC3C,MAAM,YAAY,CAAC,AAAC,KAAK,MAAM,GAAG,KAAK,YAAY,GAAI,MAAM,IAAI;oBAEjE,QAAQ,CAAC,UAAU,GAAG;wBACpB,GAAG,IAAI;wBACP,QAAQ;wBACR,cAAc;oBAChB;oBAEA,SAAS;oBAET,kDAAkD;oBAClD,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,iBAAiB;oBACpE,MAAM,iBAAiB,WAAW,SAAS,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK;oBACjE,IAAI,mBAAmB,CAAC,GAAG;wBACzB,UAAU,CAAC,eAAe,GAAG,QAAQ,CAAC,UAAU;wBAChD,aAAa,OAAO,CAAC,cAAc,KAAK,SAAS,CAAC;oBACpD;gBACF;gBACA;YACF;YAEA,wCAAwC;YACxC,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,MAAM;YAET,IAAI,gBAAgB;gBAClB,yBAAyB;gBACzB,MAAM,SACH,IAAI,CAAC,WACL,MAAM,CAAC;oBAAE;gBAAO,GAChB,EAAE,CAAC,MAAM,eAAe,EAAE;YAC/B,OAAO;gBACL,oBAAoB;gBACpB,MAAM,SACH,IAAI,CAAC,WACL,MAAM,CAAC;oBACN,SAAS;oBACT,SAAS,KAAK,EAAE;oBAChB;gBACF;YACJ;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,WACL,MAAM,CAAC,UACP,EAAE,CAAC,WAAW;YAEjB,IAAI,SAAS;gBACX,MAAM,YAAY,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE,KAAK,QAAQ,MAAM;gBAChF,MAAM,cAAc,QAAQ,MAAM;gBAElC,MAAM,SACH,IAAI,CAAC,SACL,MAAM,CAAC;oBACN,QAAQ;oBACR,cAAc;gBAChB,GACC,EAAE,CAAC,MAAM;gBAEZ,qBAAqB;gBACrB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;YAGZ;QAFrB,MAAM,gBAAgB,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,SAC1D,oBAAA,KAAK,WAAW,cAAhB,wCAAA,kBAAkB,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEpF,MAAM,gBAAgB,mBAAmB,MAAM,KAAK,MAAM,KAAK;QAC/D,MAAM,cAAc,iBAAiB,MAAM,KAAK,SAAS,KAAK;QAE9D,OAAO,iBAAiB,iBAAiB;IAC3C;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ;YACZ,KAAK;YACL,WAAW;YACX,YAAY;YACZ,OAAO;QACT;QACA,OAAO,KAAK,CAAC,KAA2B,IAAI;IAC9C;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC;IACjD;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;;;;;;sCAKhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK;oCACrF,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB;4CAAC;4CAAG;4CAAG;4CAAG;4CAAG;4CAAG;yCAAE,CAAC,GAAG,CAAC,CAAA,uBACtB,6LAAC;gDAAoB,OAAO;;oDAAS;oDAAO;;+CAA/B;;;;;;;;;;;8CAIjB,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,6LAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9B,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC;wBA+CH;yCA9Cf,6LAAC;wBAAkB,WAAU;;0CAC3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDACb,iBAAiB,KAAK,SAAS;;;;;;kDAElC,6LAAC;wCAAK,WAAU;;4CAAyB,KAAK,MAAM;4CAAC;;;;;;;;;;;;;0CAGvD,6LAAC;gCAAG,WAAU;0CACX,KAAK,KAAK;;;;;;0CAGb,6LAAC;gCAAE,WAAU;;kDACX,6LAAC;kDAAO;;;;;;oCAAiB;oCAAE,KAAK,OAAO;;;;;;;4BAGxC,KAAK,WAAW,kBACf,6LAAC;gCAAE,WAAU;0CACV,KAAK,WAAW;;;;;;0CAIrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;wCACZ;4CAAC;4CAAG;4CAAG;4CAAG;4CAAG;yCAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC;gDAEC,SAAS,IAAM,aAAa,KAAK,EAAE,EAAE;gDACrC,WAAU;0DAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDACH,WAAW,AAAC,WAIX,OAHC,QAAQ,KAAK,MAAM,GACf,iCACA,iBACL;;;;;;+CATE;;;;;sDAaT,6LAAC;4CAAK,WAAU;;gDAA6B;gDACzC,KAAK,YAAY;gDAAC;;;;;;;;;;;;;;;;;;0CAK1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAE;4CAAQ,EAAA,iBAAA,KAAK,QAAQ,cAAb,qCAAA,eAAe,SAAS,KAAI;;;;;;;kDACvC,6LAAC;;4CAAE;4CAAS,eAAe,KAAK,SAAS;;;;;;;kDACzC,6LAAC;;4CAAE;4CAAO,WAAW,KAAK,UAAU;;;;;;;;;;;;;0CAGtC,6LAAC;gCACC,MAAM,KAAK,QAAQ;gCACnB,UAAU,KAAK,SAAS;gCACxB,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;;uBAzDA,KAAK,EAAE;;;;;;;;;;;YA+DpB,cAAc,MAAM,KAAK,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;;AAOpD;GAvVwB;;QAML,0HAAA,CAAA,UAAO;;;KANF", "debugId": null}}, {"offset": {"line": 2165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D0%A0%D0%BE%D0%B1%D0%BE%D1%87%D0%B8%D0%B9%20%D1%81%D1%82%D1%96%D0%BB/ai/knure-works/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/hooks/useAuth'\nimport Navbar from '@/components/Navbar'\nimport AuthForm from '@/components/AuthForm'\nimport UploadForm from '@/components/UploadForm'\nimport WorksList from '@/components/WorksList'\n\nexport default function Home() {\n  const [currentView, setCurrentView] = useState<'works' | 'upload' | 'auth'>('works')\n  const { user, loading } = useAuth()\n\n  const handleAuthSuccess = () => {\n    setCurrentView('works')\n  }\n\n  const handleUploadSuccess = () => {\n    setCurrentView('works')\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar currentView={currentView} onViewChange={setCurrentView} />\n\n      <main className=\"container mx-auto px-4 py-8\">\n        {currentView === 'auth' && (\n          <div className=\"max-w-md mx-auto\">\n            <AuthForm onSuccess={handleAuthSuccess} />\n          </div>\n        )}\n\n        {currentView === 'upload' && user && (\n          <UploadForm onSuccess={handleUploadSuccess} />\n        )}\n\n        {currentView === 'works' && (\n          <div>\n            <div className=\"text-center mb-8\">\n              <h1 className=\"text-4xl font-bold text-gray-800 mb-4\">\n                ХНУРЕ Works\n              </h1>\n              <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n                Платформа для обміну практичними та лабораторними роботами студентів\n                Харківського національного університету радіоелектроніки\n              </p>\n            </div>\n            <WorksList />\n          </div>\n        )}\n\n        {currentView === 'upload' && !user && (\n          <div className=\"text-center py-12\">\n            <h2 className=\"text-2xl font-bold text-gray-800 mb-4\">\n              Увійдіть в систему\n            </h2>\n            <p className=\"text-gray-600 mb-6\">\n              Для завантаження робіт необхідно увійти в систему\n            </p>\n            <button\n              onClick={() => setCurrentView('auth')}\n              className=\"bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors\"\n            >\n              Увійти в систему\n            </button>\n          </div>\n        )}\n      </main>\n\n      <footer className=\"bg-white border-t border-gray-200 py-8 mt-12\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <p className=\"text-gray-600\">\n            © 2024 ХНУРЕ Works. Платформа для студентів ХНУРЕ.\n          </p>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IAC5E,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,oBAAoB;QACxB,eAAe;IACjB;IAEA,MAAM,sBAAsB;QAC1B,eAAe;IACjB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;gBAAC,aAAa;gBAAa,cAAc;;;;;;0BAEhD,6LAAC;gBAAK,WAAU;;oBACb,gBAAgB,wBACf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,iIAAA,CAAA,UAAQ;4BAAC,WAAW;;;;;;;;;;;oBAIxB,gBAAgB,YAAY,sBAC3B,6LAAC,mIAAA,CAAA,UAAU;wBAAC,WAAW;;;;;;oBAGxB,gBAAgB,yBACf,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,6LAAC;wCAAE,WAAU;kDAA0C;;;;;;;;;;;;0CAKzD,6LAAC,kIAAA,CAAA,UAAS;;;;;;;;;;;oBAIb,gBAAgB,YAAY,CAAC,sBAC5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOP,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAOvC;GA7EwB;;QAEI,0HAAA,CAAA,UAAO;;;KAFX", "debugId": null}}]}