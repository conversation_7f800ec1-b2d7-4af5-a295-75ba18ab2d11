import { createSupabaseClient } from './supabase'

export interface SignUpData {
  username: string
  fullName: string
  password: string
  confirmPassword: string
}

export interface SignInData {
  username: string
  password: string
}

// Generate a unique email for username-based auth
function generateEmailFromUsername(username: string): string {
  return `${username}@knure-works.local`
}

export async function signUpWithUsername(data: SignUpData) {
  const supabase = createSupabaseClient()
  
  if (!supabase) {
    // Demo mode
    return {
      data: {
        user: {
          id: 'demo-user-' + Date.now(),
          email: generateEmailFromUsername(data.username),
          user_metadata: {
            username: data.username,
            full_name: data.fullName
          }
        },
        session: null
      },
      error: null
    }
  }

  // Check if username already exists
  const { data: existingProfile } = await supabase
    .from('profiles')
    .select('username')
    .eq('username', data.username)
    .single()

  if (existingProfile) {
    return {
      data: null,
      error: { message: 'Користувач з таким іменем вже існує' }
    }
  }

  // Create user with generated email
  const email = generateEmailFromUsername(data.username)
  
  const { data: authData, error } = await supabase.auth.signUp({
    email,
    password: data.password,
    options: {
      data: {
        username: data.username,
        full_name: data.fullName
      }
    }
  })

  return { data: authData, error }
}

export async function signInWithUsername(data: SignInData) {
  const supabase = createSupabaseClient()
  
  if (!supabase) {
    // Demo mode
    if (data.username === 'demo' && data.password === 'demo123') {
      return {
        data: {
          user: {
            id: 'demo-user-id',
            email: generateEmailFromUsername(data.username),
            user_metadata: {
              username: data.username,
              full_name: 'Демо Користувач'
            }
          },
          session: null
        },
        error: null
      }
    } else {
      return {
        data: null,
        error: { message: 'Невірне ім\'я користувача або пароль' }
      }
    }
  }

  // Find user by username
  const { data: profile } = await supabase
    .from('profiles')
    .select('id')
    .eq('username', data.username)
    .single()

  if (!profile) {
    return {
      data: null,
      error: { message: 'Користувача з таким іменем не знайдено' }
    }
  }

  // Sign in with generated email
  const email = generateEmailFromUsername(data.username)
  
  const { data: authData, error } = await supabase.auth.signInWithPassword({
    email,
    password: data.password
  })

  return { data: authData, error }
}

export async function getUserProfile(userId: string) {
  const supabase = createSupabaseClient()
  
  if (!supabase) {
    return {
      data: {
        username: 'demo',
        full_name: 'Демо Користувач',
        university_group: null
      },
      error: null
    }
  }

  const { data, error } = await supabase
    .from('profiles')
    .select('username, full_name, university_group')
    .eq('id', userId)
    .single()

  return { data, error }
}
