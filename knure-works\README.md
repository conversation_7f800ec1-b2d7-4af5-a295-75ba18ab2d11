# ХНУРЕ Works - Платформа для студентських робіт

Веб-платформа для обміну практичними та лабораторними роботами студентів Харківського національного університету радіоелектроніки.

## Функціонал

- 🔐 **Автентифікація користувачів** - реєстрація та вхід в систему
- 📤 **Завантаження робіт** - можливість завантажувати файли з роботами
- ⭐ **Система оцінювання** - студенти можуть оцінювати роботи інших
- 🔍 **Пошук та фільтрація** - пошук за назвою, предметом, курсом та типом роботи
- 📱 **Адаптивний дизайн** - працює на всіх пристроях
- 🇺🇦 **Українська мова** - повністю українськомовний інтерфейс

## Технології

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Authentication, Storage)
- **UI**: Lucide React Icons
- **Forms**: React Hook Form + Zod validation

## Налаштування

### 1. Клонування репозиторію

```bash
git clone <repository-url>
cd knure-works
```

### 2. Встановлення залежностей

```bash
npm install
```

### 3. Налаштування Supabase

1. Створіть новий проект на [supabase.com](https://supabase.com)
2. Скопіюйте URL та anon key з налаштувань проекту
3. Створіть файл `.env.local` та додайте ваші ключі:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
```

### 4. Налаштування бази даних

1. Відкрийте SQL Editor в Supabase Dashboard
2. Виконайте SQL скрипт з файлу `supabase-setup.sql`

### 5. Запуск проекту

```bash
npm run dev
```

Відкрийте [http://localhost:3000](http://localhost:3000) у браузері.

## Структура проекту

```
src/
├── app/                    # Next.js App Router
│   ├── layout.tsx         # Головний layout
│   └── page.tsx           # Головна сторінка
├── components/            # React компоненти
│   ├── AuthForm.tsx       # Форма автентифікації
│   ├── Navbar.tsx         # Навігаційна панель
│   ├── UploadForm.tsx     # Форма завантаження робіт
│   └── WorksList.tsx      # Список робіт
├── hooks/                 # Custom React hooks
│   └── useAuth.ts         # Хук для автентифікації
└── lib/                   # Утиліти та конфігурація
    └── supabase.ts        # Конфігурація Supabase
```

## Використання

### Для студентів:

1. **Реєстрація/Вхід** - створіть акаунт або увійдіть в існуючий
2. **Перегляд робіт** - переглядайте роботи інших студентів
3. **Завантаження робіт** - завантажуйте свої роботи для інших
4. **Оцінювання** - ставте оцінки корисним роботам
5. **Пошук** - знаходьте потрібні роботи за предметом або курсом

### Типи робіт:
- Лабораторні роботи
- Практичні роботи
- Курсові роботи
- Інші типи робіт

### Підтримувані формати файлів:
- PDF
- DOC/DOCX
- TXT
- ZIP/RAR
- Максимальний розмір: 10MB

## Розробка

### Додавання нових функцій

1. Створіть нову гілку: `git checkout -b feature/new-feature`
2. Внесіть зміни
3. Протестуйте функціонал
4. Створіть Pull Request

### Корисні команди

```bash
# Запуск в режимі розробки
npm run dev

# Збірка проекту
npm run build

# Запуск збірки
npm start

# Лінтинг коду
npm run lint
```

## Ліцензія

MIT License - дивіться файл LICENSE для деталей.

## Підтримка

Якщо у вас виникли питання або проблеми, створіть issue в репозиторії.
