/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f7fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
}

.logo i {
    margin-right: 10px;
    font-size: 2rem;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.nav-link:hover,
.nav-link.active {
    background-color: rgba(255,255,255,0.2);
}

.auth-buttons {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.username {
    font-weight: bold;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background-color: #45a049;
}

.btn-outline {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn-outline:hover {
    background-color: white;
    color: #667eea;
}

/* Main Content */
.main {
    min-height: calc(100vh - 80px);
    padding: 2rem 0;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

/* Search Section */
.search-section {
    text-align: center;
    margin-bottom: 3rem;
}

.search-section h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.search-section p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
}

.search-bar {
    display: flex;
    max-width: 600px;
    margin: 0 auto 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 25px;
    overflow: hidden;
}

.search-bar input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    font-size: 1rem;
    outline: none;
}

.search-bar button {
    padding: 1rem 1.5rem;
    border-radius: 0;
}

.filters {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.filters select {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    background-color: white;
}

/* Works Grid */
.works-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.work-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
}

.work-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.work-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.work-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
}

.work-type {
    background-color: #667eea;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.work-subject {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.work-description {
    color: #666;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.work-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.work-author {
    font-size: 0.9rem;
    color: #666;
}

.work-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stars {
    color: #ffd700;
}

.rating-count {
    font-size: 0.8rem;
    color: #666;
}

/* Forms */
.upload-form,
.profile-form {
    max-width: 600px;
    margin: 0 auto;
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.form-group small {
    color: #666;
    font-size: 0.8rem;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    position: relative;
}

.modal-content.large {
    max-width: 800px;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 2rem;
    cursor: pointer;
    color: #666;
}

.close:hover {
    color: #333;
}

/* Profile */
.profile-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.stat-label {
    color: #666;
    margin-top: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav {
        gap: 1rem;
    }

    .search-bar {
        flex-direction: column;
        border-radius: 10px;
    }

    .search-bar button {
        border-radius: 0 0 10px 10px;
    }

    .filters {
        flex-direction: column;
        align-items: center;
    }

    .works-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: 10% auto;
        width: 95%;
    }
}

/* Loading and Empty States */
.loading {
    text-align: center;
    padding: 2rem;
    color: #666;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #ddd;
}

.empty-state h3,
.empty-state h4 {
    margin-bottom: 1rem;
    color: #666;
}

.empty-state .btn {
    margin-top: 1rem;
}

/* Work Details Modal */
.work-meta {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 5px;
    margin: 1rem 0;
}

.work-meta p {
    margin: 0.5rem 0;
}

.work-description-full {
    margin: 1.5rem 0;
}

.work-rating-section {
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.current-rating {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.stars.large {
    font-size: 1.5rem;
}

.rate-work {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #ddd;
}

.rating-input {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.star-input {
    font-size: 1.5rem;
    color: #ddd;
    cursor: pointer;
    transition: color 0.2s;
}

.star-input:hover,
.star-input.active {
    color: #ffd700;
}

.work-actions {
    margin-top: 1.5rem;
    text-align: center;
}

/* Profile Styles */
.profile-info {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.profile-info h3 {
    margin-bottom: 1rem;
    color: #333;
}

.profile-info p {
    margin: 0.5rem 0;
    color: #666;
}

.user-works {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.user-works h3 {
    margin-bottom: 1rem;
    color: #333;
}

.works-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.work-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 5px;
    border-left: 4px solid #667eea;
}

.work-info h4 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.work-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.work-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
    font-size: 0.9rem;
    color: #666;
}

/* Additional responsive styles */
@media (max-width: 768px) {
    .work-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .work-stats {
        align-items: flex-start;
    }

    .current-rating {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .rating-input {
        justify-content: center;
    }
}
