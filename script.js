// Application State
let currentUser = null;
let works = [];
let users = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadData();
    setupEventListeners();
    renderWorks();
});

// Initialize application
function initializeApp() {
    // Check if user is logged in
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        updateAuthUI();
    }
}

// Load data from localStorage
function loadData() {
    const savedWorks = localStorage.getItem('works');
    const savedUsers = localStorage.getItem('users');
    
    if (savedWorks) {
        works = JSON.parse(savedWorks);
    } else {
        // Add some sample data
        works = [
            {
                id: 1,
                title: "Лабораторна робота №1 з програмування",
                subject: "Програмування",
                type: "Лабораторна",
                description: "Основи роботи з масивами та циклами в C++",
                author: "Іван Петренко",
                authorId: "sample1",
                uploadDate: new Date().toISOString(),
                rating: 4.5,
                ratings: [5, 4, 5, 4, 4],
                downloads: 15,
                fileName: "lab1_programming.pdf"
            },
            {
                id: 2,
                title: "Практична робота з баз даних",
                subject: "Бази даних",
                type: "Практична",
                description: "Створення та оптимізація SQL запитів",
                author: "Марія Коваленко",
                authorId: "sample2",
                uploadDate: new Date().toISOString(),
                rating: 4.8,
                ratings: [5, 5, 4, 5, 5],
                downloads: 23,
                fileName: "practical_db.docx"
            }
        ];
        saveData();
    }
    
    if (savedUsers) {
        users = JSON.parse(savedUsers);
    }
}

// Save data to localStorage
function saveData() {
    localStorage.setItem('works', JSON.stringify(works));
    localStorage.setItem('users', JSON.stringify(users));
}

// Setup event listeners
function setupEventListeners() {
    // Navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const page = e.target.dataset.page;
            showPage(page);
        });
    });

    // Auth buttons
    document.getElementById('loginBtn').addEventListener('click', () => {
        document.getElementById('loginModal').style.display = 'block';
    });

    document.getElementById('registerBtn').addEventListener('click', () => {
        document.getElementById('registerModal').style.display = 'block';
    });

    document.getElementById('logoutBtn').addEventListener('click', logout);

    // Modal close buttons
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', (e) => {
            e.target.closest('.modal').style.display = 'none';
        });
    });

    // Close modals when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target.classList.contains('modal')) {
            e.target.style.display = 'none';
        }
    });

    // Forms
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
    document.getElementById('registerForm').addEventListener('submit', handleRegister);
    document.getElementById('uploadForm').addEventListener('submit', handleUpload);

    // Search and filters
    document.getElementById('searchBtn').addEventListener('click', performSearch);
    document.getElementById('searchInput').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') performSearch();
    });

    document.getElementById('subjectFilter').addEventListener('change', renderWorks);
    document.getElementById('typeFilter').addEventListener('change', renderWorks);
    document.getElementById('ratingFilter').addEventListener('change', renderWorks);
}

// Show specific page
function showPage(pageName) {
    // Update navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    document.querySelector(`[data-page="${pageName}"]`).classList.add('active');

    // Show page
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
    });
    document.getElementById(`${pageName}Page`).classList.add('active');

    // Load page-specific content
    if (pageName === 'profile' && currentUser) {
        loadProfile();
    }
}

// Authentication functions
function handleLogin(e) {
    e.preventDefault();
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;

    const user = users.find(u => u.email === email && u.password === password);
    
    if (user) {
        currentUser = user;
        localStorage.setItem('currentUser', JSON.stringify(currentUser));
        updateAuthUI();
        document.getElementById('loginModal').style.display = 'none';
        showNotification('Успішно увійшли в систему!', 'success');
    } else {
        showNotification('Невірний email або пароль!', 'error');
    }
}

function handleRegister(e) {
    e.preventDefault();
    const name = document.getElementById('registerName').value;
    const email = document.getElementById('registerEmail').value;
    const password = document.getElementById('registerPassword').value;
    const group = document.getElementById('registerGroup').value;

    // Check if user already exists
    if (users.find(u => u.email === email)) {
        showNotification('Користувач з таким email вже існує!', 'error');
        return;
    }

    const newUser = {
        id: Date.now().toString(),
        name,
        email,
        password,
        group,
        joinDate: new Date().toISOString(),
        uploadedWorks: 0,
        totalRating: 0
    };

    users.push(newUser);
    currentUser = newUser;
    
    localStorage.setItem('currentUser', JSON.stringify(currentUser));
    saveData();
    updateAuthUI();
    
    document.getElementById('registerModal').style.display = 'none';
    showNotification('Реєстрація успішна!', 'success');
}

function logout() {
    currentUser = null;
    localStorage.removeItem('currentUser');
    updateAuthUI();
    showPage('home');
    showNotification('Ви вийшли з системи', 'info');
}

function updateAuthUI() {
    const authButtons = document.querySelector('.auth-buttons');
    const userMenu = document.getElementById('userMenu');
    const loginBtn = document.getElementById('loginBtn');
    const registerBtn = document.getElementById('registerBtn');

    if (currentUser) {
        loginBtn.style.display = 'none';
        registerBtn.style.display = 'none';
        userMenu.style.display = 'flex';
        document.getElementById('currentUsername').textContent = currentUser.name;
    } else {
        loginBtn.style.display = 'inline-block';
        registerBtn.style.display = 'inline-block';
        userMenu.style.display = 'none';
    }
}

// Upload functionality
function handleUpload(e) {
    e.preventDefault();
    
    if (!currentUser) {
        showNotification('Для завантаження робіт потрібно увійти в систему!', 'error');
        return;
    }

    const title = document.getElementById('workTitle').value;
    const subject = document.getElementById('workSubject').value;
    const type = document.getElementById('workType').value;
    const description = document.getElementById('workDescription').value;
    const fileInput = document.getElementById('workFile');

    if (!fileInput.files[0]) {
        showNotification('Оберіть файл для завантаження!', 'error');
        return;
    }

    const file = fileInput.files[0];
    
    // Check file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
        showNotification('Файл занадто великий! Максимальний розмір: 10MB', 'error');
        return;
    }

    const newWork = {
        id: Date.now(),
        title,
        subject,
        type,
        description,
        author: currentUser.name,
        authorId: currentUser.id,
        uploadDate: new Date().toISOString(),
        rating: 0,
        ratings: [],
        downloads: 0,
        fileName: file.name,
        fileData: null // In a real app, you'd upload to a server
    };

    works.unshift(newWork);
    
    // Update user stats
    currentUser.uploadedWorks++;
    const userIndex = users.findIndex(u => u.id === currentUser.id);
    if (userIndex !== -1) {
        users[userIndex] = currentUser;
    }
    
    saveData();
    localStorage.setItem('currentUser', JSON.stringify(currentUser));
    
    document.getElementById('uploadForm').reset();
    showNotification('Робота успішно завантажена!', 'success');
    showPage('home');
    renderWorks();
}

// Search and filter functionality
function performSearch() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    renderWorks(searchTerm);
}

function renderWorks(searchTerm = '') {
    const worksGrid = document.getElementById('worksGrid');
    const subjectFilter = document.getElementById('subjectFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;
    const ratingFilter = document.getElementById('ratingFilter').value;

    let filteredWorks = works.filter(work => {
        const matchesSearch = !searchTerm || 
            work.title.toLowerCase().includes(searchTerm) ||
            work.subject.toLowerCase().includes(searchTerm) ||
            work.author.toLowerCase().includes(searchTerm) ||
            work.description.toLowerCase().includes(searchTerm);
        
        const matchesSubject = !subjectFilter || work.subject === subjectFilter;
        const matchesType = !typeFilter || work.type === typeFilter;
        const matchesRating = !ratingFilter || work.rating >= parseInt(ratingFilter);

        return matchesSearch && matchesSubject && matchesType && matchesRating;
    });

    if (filteredWorks.length === 0) {
        worksGrid.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-search"></i>
                <h3>Роботи не знайдено</h3>
                <p>Спробуйте змінити параметри пошуку</p>
            </div>
        `;
        return;
    }

    worksGrid.innerHTML = filteredWorks.map(work => `
        <div class="work-card" onclick="showWorkDetails(${work.id})">
            <div class="work-header">
                <div>
                    <div class="work-title">${work.title}</div>
                    <div class="work-subject">${work.subject}</div>
                </div>
                <div class="work-type">${work.type}</div>
            </div>
            <div class="work-description">${work.description}</div>
            <div class="work-footer">
                <div class="work-author">
                    <i class="fas fa-user"></i> ${work.author}
                </div>
                <div class="work-rating">
                    <div class="stars">${generateStars(work.rating)}</div>
                    <span class="rating-count">(${work.ratings.length})</span>
                </div>
            </div>
        </div>
    `).join('');
}

// Generate star rating display
function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    return '★'.repeat(fullStars) + 
           (hasHalfStar ? '☆' : '') + 
           '☆'.repeat(emptyStars);
}

// Show work details modal
function showWorkDetails(workId) {
    const work = works.find(w => w.id === workId);
    if (!work) return;

    const workDetails = document.getElementById('workDetails');
    const canRate = currentUser && currentUser.id !== work.authorId;
    
    workDetails.innerHTML = `
        <h2>${work.title}</h2>
        <div class="work-meta">
            <p><strong>Предмет:</strong> ${work.subject}</p>
            <p><strong>Тип:</strong> ${work.type}</p>
            <p><strong>Автор:</strong> ${work.author}</p>
            <p><strong>Дата завантаження:</strong> ${new Date(work.uploadDate).toLocaleDateString('uk-UA')}</p>
            <p><strong>Завантажень:</strong> ${work.downloads}</p>
        </div>
        <div class="work-description-full">
            <h3>Опис:</h3>
            <p>${work.description || 'Опис відсутній'}</p>
        </div>
        <div class="work-rating-section">
            <h3>Оцінка:</h3>
            <div class="current-rating">
                <div class="stars large">${generateStars(work.rating)}</div>
                <span>${work.rating.toFixed(1)} (${work.ratings.length} оцінок)</span>
            </div>
            ${canRate ? `
                <div class="rate-work">
                    <p>Оцініть цю роботу:</p>
                    <div class="rating-input">
                        ${[1,2,3,4,5].map(star => `
                            <span class="star-input" data-rating="${star}" onclick="rateWork(${work.id}, ${star})">☆</span>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
        </div>
        <div class="work-actions">
            <button class="btn btn-primary" onclick="downloadWork(${work.id})">
                <i class="fas fa-download"></i> Завантажити
            </button>
        </div>
    `;

    document.getElementById('workModal').style.display = 'block';
}

// Rate work
function rateWork(workId, rating) {
    if (!currentUser) {
        showNotification('Для оцінювання потрібно увійти в систему!', 'error');
        return;
    }

    const work = works.find(w => w.id === workId);
    if (!work) return;

    if (currentUser.id === work.authorId) {
        showNotification('Ви не можете оцінювати власні роботи!', 'error');
        return;
    }

    // Add rating
    work.ratings.push(rating);
    work.rating = work.ratings.reduce((sum, r) => sum + r, 0) / work.ratings.length;

    saveData();
    showNotification('Дякуємо за оцінку!', 'success');
    showWorkDetails(workId); // Refresh the modal
    renderWorks(); // Refresh the grid
}

// Download work
function downloadWork(workId) {
    const work = works.find(w => w.id === workId);
    if (!work) return;

    // Increment download count
    work.downloads++;
    saveData();

    // In a real application, this would download the actual file
    showNotification(`Завантаження "${work.fileName}" розпочато!`, 'info');
    
    // Refresh the modal to show updated download count
    showWorkDetails(workId);
}

// Load profile page
function loadProfile() {
    if (!currentUser) {
        document.getElementById('profileContent').innerHTML = `
            <div class="empty-state">
                <i class="fas fa-user"></i>
                <h3>Увійдіть в систему</h3>
                <p>Для перегляду профілю потрібно увійти в систему</p>
            </div>
        `;
        return;
    }

    const userWorks = works.filter(w => w.authorId === currentUser.id);
    const totalDownloads = userWorks.reduce((sum, w) => sum + w.downloads, 0);
    const avgRating = userWorks.length > 0 ? 
        userWorks.reduce((sum, w) => sum + w.rating, 0) / userWorks.length : 0;

    document.getElementById('profileContent').innerHTML = `
        <div class="profile-info">
            <h3>Інформація про користувача</h3>
            <p><strong>Ім'я:</strong> ${currentUser.name}</p>
            <p><strong>Email:</strong> ${currentUser.email}</p>
            <p><strong>Група:</strong> ${currentUser.group || 'Не вказано'}</p>
            <p><strong>Дата реєстрації:</strong> ${new Date(currentUser.joinDate).toLocaleDateString('uk-UA')}</p>
        </div>

        <div class="profile-stats">
            <div class="stat-card">
                <div class="stat-number">${userWorks.length}</div>
                <div class="stat-label">Завантажено робіт</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${totalDownloads}</div>
                <div class="stat-label">Загальних завантажень</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${avgRating.toFixed(1)}</div>
                <div class="stat-label">Середня оцінка</div>
            </div>
        </div>

        <div class="user-works">
            <h3>Мої роботи</h3>
            ${userWorks.length > 0 ? `
                <div class="works-list">
                    ${userWorks.map(work => `
                        <div class="work-item">
                            <div class="work-info">
                                <h4>${work.title}</h4>
                                <p>${work.subject} - ${work.type}</p>
                            </div>
                            <div class="work-stats">
                                <span>${generateStars(work.rating)} (${work.ratings.length})</span>
                                <span>${work.downloads} завантажень</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            ` : '<p>Ви ще не завантажили жодної роботи.</p>'}
        </div>
    `;
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">×</button>
    `;

    // Add styles if not already added
    if (!document.querySelector('#notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 5px;
                color: white;
                z-index: 10000;
                display: flex;
                align-items: center;
                gap: 1rem;
                animation: slideIn 0.3s ease;
            }
            .notification.success { background-color: #4CAF50; }
            .notification.error { background-color: #f44336; }
            .notification.info { background-color: #2196F3; }
            .notification button {
                background: none;
                border: none;
                color: white;
                font-size: 1.2rem;
                cursor: pointer;
            }
            @keyframes slideIn {
                from { transform: translateX(100%); }
                to { transform: translateX(0); }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}
