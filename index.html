<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ХНУРЕ - Банк студентських робіт</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-graduation-cap"></i>
                <span>ХНУРЕ Банк Робіт</span>
            </div>
            <nav class="nav">
                <a href="#" class="nav-link active" data-page="home">Головна</a>
                <a href="#" class="nav-link" data-page="upload">Завантажити</a>
                <a href="#" class="nav-link" data-page="profile">Профіль</a>
            </nav>
            <div class="auth-buttons">
                <button class="btn btn-outline" id="loginBtn">Увійти</button>
                <button class="btn btn-primary" id="registerBtn">Реєстрація</button>
                <div class="user-menu" id="userMenu" style="display: none;">
                    <span class="username" id="currentUsername"></span>
                    <button class="btn btn-outline" id="logoutBtn">Вийти</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Home Page -->
        <div class="page active" id="homePage">
            <div class="container">
                <div class="search-section">
                    <h1>Банк студентських робіт ХНУРЕ</h1>
                    <p>Безкоштовна платформа для обміну практичними та лабораторними роботами</p>
                    
                    <div class="search-bar">
                        <input type="text" id="searchInput" placeholder="Пошук робіт за назвою, предметом або автором...">
                        <button class="btn btn-primary" id="searchBtn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>

                    <div class="filters">
                        <select id="subjectFilter">
                            <option value="">Всі предмети</option>
                            <option value="Програмування">Програмування</option>
                            <option value="Математика">Математика</option>
                            <option value="Фізика">Фізика</option>
                            <option value="Електроніка">Електроніка</option>
                            <option value="Комп'ютерні мережі">Комп'ютерні мережі</option>
                            <option value="Бази даних">Бази даних</option>
                        </select>
                        
                        <select id="typeFilter">
                            <option value="">Всі типи</option>
                            <option value="Лабораторна">Лабораторна робота</option>
                            <option value="Практична">Практична робота</option>
                            <option value="Курсова">Курсова робота</option>
                            <option value="Реферат">Реферат</option>
                        </select>

                        <select id="ratingFilter">
                            <option value="">Всі оцінки</option>
                            <option value="5">5 зірок</option>
                            <option value="4">4+ зірок</option>
                            <option value="3">3+ зірок</option>
                        </select>
                    </div>
                </div>

                <div class="works-grid" id="worksGrid">
                    <!-- Works will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Upload Page -->
        <div class="page" id="uploadPage">
            <div class="container">
                <h2>Завантажити роботу</h2>
                <form class="upload-form" id="uploadForm">
                    <div class="form-group">
                        <label for="workTitle">Назва роботи *</label>
                        <input type="text" id="workTitle" required>
                    </div>

                    <div class="form-group">
                        <label for="workSubject">Предмет *</label>
                        <select id="workSubject" required>
                            <option value="">Оберіть предмет</option>
                            <option value="Програмування">Програмування</option>
                            <option value="Математика">Математика</option>
                            <option value="Фізика">Фізика</option>
                            <option value="Електроніка">Електроніка</option>
                            <option value="Комп'ютерні мережі">Комп'ютерні мережі</option>
                            <option value="Бази даних">Бази даних</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="workType">Тип роботи *</label>
                        <select id="workType" required>
                            <option value="">Оберіть тип</option>
                            <option value="Лабораторна">Лабораторна робота</option>
                            <option value="Практична">Практична робота</option>
                            <option value="Курсова">Курсова робота</option>
                            <option value="Реферат">Реферат</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="workDescription">Опис роботи</label>
                        <textarea id="workDescription" rows="4" placeholder="Короткий опис роботи, що вона включає..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="workFile">Файл роботи *</label>
                        <input type="file" id="workFile" accept=".pdf,.doc,.docx,.zip,.rar" required>
                        <small>Підтримувані формати: PDF, DOC, DOCX, ZIP, RAR (макс. 10MB)</small>
                    </div>

                    <button type="submit" class="btn btn-primary">Завантажити роботу</button>
                </form>
            </div>
        </div>

        <!-- Profile Page -->
        <div class="page" id="profilePage">
            <div class="container">
                <h2>Мій профіль</h2>
                <div class="profile-content" id="profileContent">
                    <!-- Profile content will be loaded here -->
                </div>
            </div>
        </div>
    </main>

    <!-- Login Modal -->
    <div class="modal" id="loginModal">
        <div class="modal-content">
            <span class="close" id="closeLogin">&times;</span>
            <h2>Увійти</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="loginEmail">Email</label>
                    <input type="email" id="loginEmail" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">Пароль</label>
                    <input type="password" id="loginPassword" required>
                </div>
                <button type="submit" class="btn btn-primary">Увійти</button>
            </form>
        </div>
    </div>

    <!-- Register Modal -->
    <div class="modal" id="registerModal">
        <div class="modal-content">
            <span class="close" id="closeRegister">&times;</span>
            <h2>Реєстрація</h2>
            <form id="registerForm">
                <div class="form-group">
                    <label for="registerName">Ім'я *</label>
                    <input type="text" id="registerName" required>
                </div>
                <div class="form-group">
                    <label for="registerEmail">Email *</label>
                    <input type="email" id="registerEmail" required>
                </div>
                <div class="form-group">
                    <label for="registerPassword">Пароль *</label>
                    <input type="password" id="registerPassword" required>
                </div>
                <div class="form-group">
                    <label for="registerGroup">Група</label>
                    <input type="text" id="registerGroup" placeholder="Наприклад: КН-21-1">
                </div>
                <button type="submit" class="btn btn-primary">Зареєструватися</button>
            </form>
        </div>
    </div>

    <!-- Work Details Modal -->
    <div class="modal" id="workModal">
        <div class="modal-content large">
            <span class="close" id="closeWork">&times;</span>
            <div id="workDetails">
                <!-- Work details will be loaded here -->
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
