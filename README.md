# ХНУРЕ - Банк студентських робіт

Локальний веб-сайт для зберігання та обміну практичними і лабораторними роботами студентів Харківського національного університету радіоелектроніки.

## Особливості

### 🎓 Для студентів
- **Безкоштовне зберігання робіт** - завантажуйте свої роботи без обмежень
- **Обмін знаннями** - діліться досвідом з іншими студентами
- **Система оцінювання** - оцінюйте корисність робіт інших студентів
- **Пошук та фільтрація** - легко знаходьте потрібні роботи

### 🔧 Технічні можливості
- **Чисто клієнтський додаток** - працює без сервера
- **Локальне зберігання** - всі дані зберігаються в браузері
- **Адаптивний дизайн** - працює на всіх пристроях
- **Інтуїтивний інтерфейс** - простий у використанні

## Як користуватися

### Запуск сайту
1. Завантажте всі файли проекту
2. Відкрийте файл `index.html` в браузері
3. Сайт готовий до використання!

### Реєстрація та вхід
1. Натисніть кнопку "Реєстрація" у верхньому правому куті
2. Заповніть форму реєстрації:
   - Ім'я
   - Email
   - Пароль
   - Група (необов'язково)
3. Після реєстрації ви автоматично увійдете в систему

### Завантаження робіт
1. Увійдіть в систему
2. Перейдіть на сторінку "Завантажити"
3. Заповніть інформацію про роботу:
   - Назва роботи
   - Предмет
   - Тип роботи (лабораторна, практична, курсова, реферат)
   - Опис роботи
   - Файл роботи (PDF, DOC, DOCX, ZIP, RAR до 10MB)
4. Натисніть "Завантажити роботу"

### Пошук робіт
1. Використовуйте пошукову строку на головній сторінці
2. Застосовуйте фільтри:
   - За предметом
   - За типом роботи
   - За рейтингом
3. Натискайте на картки робіт для детального перегляду

### Оцінювання робіт
1. Відкрийте деталі роботи
2. Поставте оцінку від 1 до 5 зірок
3. Ваша оцінка буде врахована в загальному рейтингу

### Профіль користувача
1. Перейдіть на сторінку "Профіль"
2. Переглядайте статистику:
   - Кількість завантажених робіт
   - Загальна кількість завантажень ваших робіт
   - Середня оцінка ваших робіт
3. Переглядайте список ваших робіт

## Підтримувані формати файлів

- **PDF** - для готових документів
- **DOC/DOCX** - документи Microsoft Word
- **ZIP/RAR** - архіви з кодом або множинними файлами

## Предмети

Сайт підтримує роботи з наступних предметів:
- Програмування
- Математика
- Фізика
- Електроніка
- Комп'ютерні мережі
- Бази даних

*Список можна розширити, редагуючи файли HTML та JavaScript*

## Типи робіт

- **Лабораторні роботи** - практичні завдання з лабораторій
- **Практичні роботи** - самостійні практичні завдання
- **Курсові роботи** - великі проекти на семестр
- **Реферати** - теоретичні дослідження

## Безпека та конфіденційність

- Всі дані зберігаються локально в браузері
- Паролі зберігаються в відкритому вигляді (для навчальних цілей)
- Файли не завантажуються на сервер
- Дані не передаються третім особам

## Технічні деталі

### Структура проекту
```
├── index.html          # Головна сторінка
├── styles.css          # Стилі CSS
├── script.js           # JavaScript функціональність
└── README.md           # Документація
```

### Використані технології
- **HTML5** - структура сторінки
- **CSS3** - стилізація та адаптивний дизайн
- **JavaScript (ES6+)** - функціональність
- **LocalStorage** - зберігання даних
- **Font Awesome** - іконки

### Браузерна сумісність
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Розширення функціональності

### Додавання нових предметів
1. Відкрийте `index.html`
2. Знайдіть секції з `<select id="subjectFilter">` та `<select id="workSubject">`
3. Додайте нові `<option>` елементи

### Додавання нових типів робіт
1. Відкрийте `index.html`
2. Знайдіть секції з `<select id="typeFilter">` та `<select id="workType">`
3. Додайте нові `<option>` елементи

### Зміна дизайну
1. Редагуйте файл `styles.css`
2. Змінюйте кольори, шрифти, розміри за потребою

## Обмеження

- Файли не зберігаються на сервері (тільки метадані)
- Дані видаляються при очищенні браузера
- Максимальний розмір файлу: 10MB
- Працює тільки в одному браузері на одному пристрої

## Підтримка

Для питань та пропозицій щодо покращення сайту:
1. Створіть issue в репозиторії
2. Опишіть проблему або пропозицію
3. Додайте скріншоти при необхідності

## Ліцензія

Проект створений для навчальних цілей та вільного використання студентами ХНУРЕ.

---

**Створено для студентів ХНУРЕ з ❤️**
