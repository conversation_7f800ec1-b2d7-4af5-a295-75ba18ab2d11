'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useAuth } from '@/hooks/useAuth'

const authSchema = z.object({
  username: z.string()
    .min(3, 'Ім\'я користувача повинно містити мінімум 3 символи')
    .max(20, 'Ім\'я користувача не може містити більше 20 символів')
    .regex(/^[a-zA-Z0-9_]+$/, 'Ім\'я користувача може містити тільки літери, цифри та підкреслення'),
  password: z.string().min(6, 'Пароль повинен містити мінімум 6 символів'),
  confirmPassword: z.string().optional(),
  fullName: z.string().min(2, 'Повне ім\'я повинно містити мінімум 2 символи').optional(),
}).refine((data) => {
  if (data.confirmPassword !== undefined) {
    return data.password === data.confirmPassword
  }
  return true
}, {
  message: 'Паролі не співпадають',
  path: ['confirmPassword']
})

type AuthFormData = z.infer<typeof authSchema>

interface AuthFormProps {
  onSuccess?: () => void
}

export default function AuthForm({ onSuccess }: AuthFormProps) {
  const [isSignUp, setIsSignUp] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { signIn, signUp } = useAuth()

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<AuthFormData>({
    resolver: zodResolver(authSchema),
  })

  const onSubmit = async (data: AuthFormData) => {
    setLoading(true)
    setError(null)

    try {
      if (isSignUp) {
        if (!data.fullName) {
          setError('Повне ім\'я є обов\'язковим для реєстрації')
          return
        }

        const signUpData = {
          username: data.username,
          fullName: data.fullName,
          password: data.password,
          confirmPassword: data.confirmPassword || data.password
        }

        const { error } = await signUp(signUpData)
        if (error) {
          setError(error.message)
        } else {
          setError(null)
          onSuccess?.()
          alert('Реєстрація успішна! Ви увійшли в систему.')
        }
      } else {
        const signInData = {
          username: data.username,
          password: data.password
        }

        const { error } = await signIn(signInData)
        if (error) {
          setError(error.message)
        } else {
          onSuccess?.()
        }
      }
    } catch (err) {
      setError('Сталася помилка. Спробуйте ще раз.')
    } finally {
      setLoading(false)
    }
  }

  const toggleMode = () => {
    setIsSignUp(!isSignUp)
    setError(null)
    reset()
  }

  return (
    <div className="max-w-md mx-auto bg-white p-8 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold text-center mb-6 text-gray-800">
        {isSignUp ? 'Реєстрація' : 'Вхід'}
      </h2>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {isSignUp && (
          <div>
            <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
              Повне ім'я *
            </label>
            <input
              {...register('fullName')}
              type="text"
              id="fullName"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Введіть ваше повне ім'я"
            />
            {errors.fullName && (
              <p className="text-red-500 text-sm mt-1">{errors.fullName.message}</p>
            )}
          </div>
        )}

        <div>
          <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
            Ім'я користувача *
          </label>
          <input
            {...register('username')}
            type="text"
            id="username"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Введіть ім'я користувача"
          />
          {errors.username && (
            <p className="text-red-500 text-sm mt-1">{errors.username.message}</p>
          )}
          <p className="text-xs text-gray-500 mt-1">
            Тільки літери, цифри та підкреслення. 3-20 символів.
          </p>
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
            Пароль *
          </label>
          <input
            {...register('password')}
            type="password"
            id="password"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Введіть ваш пароль"
          />
          {errors.password && (
            <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>
          )}
        </div>

        {isSignUp && (
          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
              Підтвердження паролю *
            </label>
            <input
              {...register('confirmPassword')}
              type="password"
              id="confirmPassword"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Підтвердіть ваш пароль"
            />
            {errors.confirmPassword && (
              <p className="text-red-500 text-sm mt-1">{errors.confirmPassword.message}</p>
            )}
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        <button
          type="submit"
          disabled={loading}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Завантаження...' : (isSignUp ? 'Зареєструватися' : 'Увійти')}
        </button>
      </form>

      <div className="mt-6 text-center">
        <button
          onClick={toggleMode}
          className="text-blue-600 hover:text-blue-800 text-sm"
        >
          {isSignUp 
            ? 'Вже маєте акаунт? Увійти' 
            : 'Немає акаунту? Зареєструватися'
          }
        </button>
      </div>
    </div>
  )
}
