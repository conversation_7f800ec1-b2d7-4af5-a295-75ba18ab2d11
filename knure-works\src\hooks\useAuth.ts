'use client'

import { useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { createSupabaseClient } from '@/lib/supabase'
import { signUpWithUsername, signInWithUsername, SignUpData, SignInData, getUserProfile } from '@/lib/auth'

interface UserProfile {
  username: string
  full_name: string
  university_group: string | null
}

interface ExtendedUser extends User {
  profile?: UserProfile
}

export function useAuth() {
  const [user, setUser] = useState<ExtendedUser | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  const loadUserProfile = async (authUser: User) => {
    const { data: profile } = await getUserProfile(authUser.id)
    return {
      ...authUser,
      profile: profile || undefined
    } as ExtendedUser
  }

  useEffect(() => {
    if (!supabase) {
      // Demo mode - simulate loading
      setTimeout(() => {
        setLoading(false)
      }, 1000)
      return
    }

    // Get initial session
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      if (session?.user) {
        const userWithProfile = await loadUserProfile(session.user)
        setUser(userWithProfile)
      } else {
        setUser(null)
      }
      setLoading(false)
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          const userWithProfile = await loadUserProfile(session.user)
          setUser(userWithProfile)
        } else {
          setUser(null)
        }
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [supabase])

  const signIn = async (data: SignInData) => {
    const result = await signInWithUsername(data)
    if (result.data?.user && !result.error) {
      const userWithProfile = await loadUserProfile(result.data.user)
      setUser(userWithProfile)
    }
    return result
  }

  const signUp = async (data: SignUpData) => {
    const result = await signUpWithUsername(data)
    if (result.data?.user && !result.error) {
      const userWithProfile = await loadUserProfile(result.data.user)
      setUser(userWithProfile)
    }
    return result
  }

  const signOut = async () => {
    if (!supabase) {
      // Demo mode
      setUser(null)
      return { error: null }
    }

    const { error } = await supabase.auth.signOut()
    return { error }
  }

  return {
    user,
    loading,
    signIn,
    signUp,
    signOut,
  }
}
