'use client'

import { useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { createSupabaseClient } from '@/lib/supabase'

// Demo user for testing
const demoUser: User = {
  id: 'demo-user-id',
  email: '<EMAIL>',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  aud: 'authenticated',
  app_metadata: {},
  user_metadata: { full_name: 'Демо Користувач' },
  role: 'authenticated',
  email_confirmed_at: new Date().toISOString(),
  phone_confirmed_at: null,
  confirmation_sent_at: null,
  confirmed_at: new Date().toISOString(),
  last_sign_in_at: new Date().toISOString(),
  recovery_sent_at: null,
  new_email: null,
  new_phone: null,
  invited_at: null,
  action_link: null,
  phone: null,
  is_anonymous: false,
  factors: null,
  identities: []
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (!supabase) {
      // Demo mode - simulate loading
      setTimeout(() => {
        setLoading(false)
      }, 1000)
      return
    }

    // Get initial session
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      setUser(session?.user ?? null)
      setLoading(false)
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null)
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [supabase])

  const signIn = async (email: string, password: string) => {
    if (!supabase) {
      // Demo mode
      if (email === '<EMAIL>' && password === 'demo123') {
        setUser(demoUser)
        return { data: { user: demoUser, session: null }, error: null }
      } else {
        return { data: null, error: { message: 'Невірний email або пароль' } }
      }
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { data, error }
  }

  const signUp = async (email: string, password: string, fullName: string) => {
    if (!supabase) {
      // Demo mode
      const newUser = { ...demoUser, email, user_metadata: { full_name: fullName } }
      setUser(newUser)
      return { data: { user: newUser, session: null }, error: null }
    }

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
      },
    })
    return { data, error }
  }

  const signOut = async () => {
    if (!supabase) {
      // Demo mode
      setUser(null)
      return { error: null }
    }

    const { error } = await supabase.auth.signOut()
    return { error }
  }

  return {
    user,
    loading,
    signIn,
    signUp,
    signOut,
  }
}
