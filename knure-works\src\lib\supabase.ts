import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo-key'

// For demo purposes, we'll create a mock client if no real credentials are provided
const isDemo = supabaseUrl === 'https://demo.supabase.co'

export const supabase = isDemo ? null : createClient(supabaseUrl, supabaseAnonKey)

export function createSupabaseClient() {
  if (isDemo) {
    return null
  }
  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}

export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          university_group: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          university_group?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          university_group?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      works: {
        Row: {
          id: string
          title: string
          description: string | null
          subject: string
          course: number
          work_type: 'lab' | 'practical' | 'coursework' | 'other'
          file_url: string
          file_name: string
          file_size: number
          author_id: string
          rating: number
          rating_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          subject: string
          course: number
          work_type: 'lab' | 'practical' | 'coursework' | 'other'
          file_url: string
          file_name: string
          file_size: number
          author_id: string
          rating?: number
          rating_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          subject?: string
          course?: number
          work_type?: 'lab' | 'practical' | 'coursework' | 'other'
          file_url?: string
          file_name?: string
          file_size?: number
          author_id?: string
          rating?: number
          rating_count?: number
          created_at?: string
          updated_at?: string
        }
      }
      ratings: {
        Row: {
          id: string
          work_id: string
          user_id: string
          rating: number
          created_at: string
        }
        Insert: {
          id?: string
          work_id: string
          user_id: string
          rating: number
          created_at?: string
        }
        Update: {
          id?: string
          work_id?: string
          user_id?: string
          rating?: number
          created_at?: string
        }
      }
    }
  }
}
